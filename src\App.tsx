import { BrowserRouter as Router, Route, Routes } from "react-router-dom";
import { Toaster } from "react-hot-toast";
import Dashboard from "./pages/Dashboard";
import { SelfEmployedVerificationStage1 } from "./pages/VerificationStage1/SelfEmployedVerificationStage1";
import { StudentVerification } from "./pages/VerificationStage1/StudentVerification";
import { AdminAssignment } from "./pages/Assignment/AdminAssignment";
import { SalesAdminAssignment } from "./pages/Assignment/SalesAdminAssignment";
import AssignmentIndex from "./pages/Assignment/Index";
import { DocumentCheck } from "./pages/DocumentCheck/DocumentCheck";
import { BasePage } from "./pages/BasePage";
import StageOneVerification from "./pages/VerificationStage1/StageOneTable";
import { NextPay } from "./pages/NextPay";
import { Defaulters } from "./pages/Defaulters";
import Blacklist from "./pages/Blacklist/Blacklist";
import { NewsUpdate } from "./pages/NewsUpdate";
import { CreateAdmin } from "./pages/CreateAdmin";
import { Store } from "./pages/Store/Store";
import { Faq } from "./pages/Faq";
import Login from "./pages/Authentication/Login";
import Users from "./pages/Users/<USER>";
import { Order } from "./pages/Orders/Order";
import { Repayment } from "./pages/Repayment";
import { Logistics } from "./pages/Logistics/Logistics";
import { RiderActivity } from "./pages/Logistics/RiderActivity";
import { BusyRiders } from "./pages/Logistics/BusyRiders";
import { AssignDelivery } from "./pages/Logistics/AssignDelivery";
import { AssignedOrders } from "./pages/Logistics/AssignedOrders";
import { AllDeliveries } from "./pages/Logistics/AllDeliveries";
import { Disbutes } from "./pages/Logistics/Disbutes";
import AddRider from "./pages/Logistics/AddRider";
import { VirtualAccount } from "./pages/VirtualAccount";
import { Emandate } from "./pages/Emandate/Emandate";
import { Restaurants } from "./pages/Restaurant/Restaurants";
import { RestaurantInfo } from "./pages/Restaurant/RestaurantInfo";
import { CustomerCare } from "./pages/CustomerService/CustomerCare";
import { Settlement } from "./pages/Restaurant/Settlement";
import AddRestaurant from "./pages/Restaurant/AddRestaurant";
import InstituteUpload from "./pages/InstituteUpload";
import Vendors from "./pages/Vendor/Vendors";
import VendorInfo from "./pages/Vendor/VendorInfo";
import { VendorSettlement } from "./pages/Vendor/VendorSettlement";
import Dietitian from "./pages/Dietitian/Dietitian";
import Staff from "./pages/Staff/Staff";
import DietitianRequests from "./pages/Dietitian/DietitianRequests";
import ProtectedRoute from "./components/ProtectedRoute";
import Operations from "./pages/Operations";
import CustomerChat from "./pages/CustomerService/CustomerChat";
import UserDetails from "./pages/Users/<USER>";
import CreditScorePage from "./pages/CreditScore/CreditScorePage";
import CreditDetails from "./pages/CreditScore/CreditDetails";
import PendingEmandates from "./pages/Emandate/PendingEmandate";
import Index from "./pages/Packages/Index";
import ReferralIndex from "./pages/Referrals/ReferralIndex";
import OrderHistory from "./pages/Orders/OrderHistory";
import Analytics from "./pages/Dashboard/Analytics";
import Notification from "./pages/Notification";
import AllCanceledMandates from "./pages/Emandate/AllCanceledMandates";
import ReferrerDetails from "./pages/Referrals/ReferrerDetails";
import IncompleteUsers from "./pages/IncompleteUsers/IncompleteUsers";
import PushNotication from "./pages/PushNotication";
import WebVisitors from "./pages/WebVisitors/Index";
import StateGovernment from "./pages/StateGov/Index";
import Churches from "./pages/Churches";

import Mfbs from "./pages/Mfbs/Index";
import OyoVerification from "./pages/VerificationStage1/OyoVerification";
import { OyoDocumentCheck } from "./pages/DocumentCheck/OyoDocumentCheck";
import { StateGovernmentOrders } from "./pages/StateGovernmentOrder/Orders";
import { ChurchOrders } from "./pages/ChurchOrders/Orders";
import { PrivateOrgOrders } from "./pages/PrivateOrgOrders/Orders";
import WareHouse from "./pages/WareHouseMgt/WareHouse";
import WareHouseList from "./pages/WareHouseMgt/WareHouseList";
import StoreDetail from "./pages/WareHouseMgt/StoreDetail";
import AddStateForm from "./pages/WareHouseMgt/AddStateForm";
import { SalesAdminPages } from "./pages/Assignment/SalesAdminPages";
import { ChurchDocumentCheck } from "./pages/DocumentCheck/ChurchDocumentCheck";
import { PrivateOrgDocumentCheck } from "./pages/DocumentCheck/PrivateOrgDocumentCheck";
import PrivateOrganizations from "./pages/PrivateOrganizations/PrivateOrganization";
import UserCategory from "./pages/UserCategory";
import EmailPreview from "./emailTemplates/EmailPreview";
import SalesReferral from "./pages/Referrals/SalesReferral";
import SalesDetails from "./pages/Referrals/SalesDetails";
import Transactions from "./pages/Transactions/Transactions";
import UserCategoryTable from "./pages/Users/<USER>";
import OrderDemographics from "./pages/OrderDemographics";
import FeatureSwitch from "./pages/FeatureSwitch/FeatureSwitch";
import AgentsIndex from "./pages/Agents/Index";
import StateUsersWithOrders from "./pages/StateGovernmentOrder/StateUsersWithOrders";

function App() {
  return (
    <>
      <Toaster position="top-right" />
      <Router>
        <Routes>
          <Route path="/" element={<Login />} />
          <Route path="/preview/email" element={<EmailPreview />} />
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <BasePage />
              </ProtectedRoute>
            }
          >
            <Route
              index
              element={
                <ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="analytics"
              element={
                <ProtectedRoute>
                  <Analytics />
                </ProtectedRoute>
              }
            />
            <Route
              path="push-notification"
              element={
                <ProtectedRoute>
                  <PushNotication />
                </ProtectedRoute>
              }
            />
            <Route
              path="notifications"
              element={
                <ProtectedRoute>
                  <Notification />
                </ProtectedRoute>
              }
            />
            <Route
              path="web-visitors"
              element={
                <ProtectedRoute>
                  <WebVisitors />
                </ProtectedRoute>
              }
            />
            <Route
              path="feature-switch"
              element={
                <ProtectedRoute>
                  <FeatureSwitch />
                </ProtectedRoute>
              }
            />
            <Route
              path="users"
              element={
                <ProtectedRoute>
                  <Users />
                </ProtectedRoute>
              }
            />
            <Route
              path="user-details"
              element={
                <ProtectedRoute>
                  <UserDetails />
                </ProtectedRoute>
              }
            />
            <Route
              path="user-category-table"
              element={
                <ProtectedRoute>
                  <UserCategoryTable />
                </ProtectedRoute>
              }
            />
            <Route
              path="agents"
              element={
                <ProtectedRoute>
                  <AgentsIndex />
                </ProtectedRoute>
              }
            />
            <Route
              path="order-demographics"
              element={
                <ProtectedRoute>
                  <OrderDemographics />
                </ProtectedRoute>
              }
            />
            <Route
              path="incomplete-users"
              element={
                <ProtectedRoute>
                  <IncompleteUsers />
                </ProtectedRoute>
              }
            />
            <Route
              path="virtual-account"
              element={
                <ProtectedRoute>
                  <VirtualAccount />
                </ProtectedRoute>
              }
            />
            <Route path="order-requests" element={<Order />} />
            <Route path="order-history" element={<OrderHistory />} />
            <Route
              path="state-government-orders"
              element={<StateGovernmentOrders />}
            />
            <Route
              path="state-government-orders/users-with-orders"
              element={
                <ProtectedRoute>
                  <StateUsersWithOrders />
                </ProtectedRoute>
              }
            />

            <Route path="church-orders" element={<ChurchOrders />} />
            <Route path="private-org-orders" element={<PrivateOrgOrders />} />
            <Route
              path="employed-verification1"
              element={
                <ProtectedRoute>
                  <StageOneVerification />
                </ProtectedRoute>
              }
            />
            <Route
              path="self-employed-verification1"
              element={
                <ProtectedRoute>
                  <SelfEmployedVerificationStage1 />
                </ProtectedRoute>
              }
            />
            <Route
              path="student-verification1"
              element={
                <ProtectedRoute>
                  <StudentVerification />
                </ProtectedRoute>
              }
            />
            <Route
              path="oyo-state-verification"
              element={
                <ProtectedRoute>
                  <OyoVerification />
                </ProtectedRoute>
              }
            />
            <Route
              path="oyo-document-check"
              element={
                <ProtectedRoute>
                  <OyoDocumentCheck />
                </ProtectedRoute>
              }
            />
            <Route
              path="church-document-check"
              element={
                <ProtectedRoute>
                  <ChurchDocumentCheck />
                </ProtectedRoute>
              }
            />
            <Route
              path="private-org-document-check"
              element={
                <ProtectedRoute>
                  <PrivateOrgDocumentCheck />
                </ProtectedRoute>
              }
            />
            <Route
              path="institute-upload"
              element={
                <ProtectedRoute>
                  <InstituteUpload />
                </ProtectedRoute>
              }
            />
            <Route
              path="state-government"
              element={
                <ProtectedRoute>
                  <StateGovernment />
                </ProtectedRoute>
              }
            />
            <Route
              path="churches"
              element={
                <ProtectedRoute>
                  <Churches />
                </ProtectedRoute>
              }
            />
            <Route
              path="private-organizations"
              element={
                <ProtectedRoute>
                  <PrivateOrganizations />
                </ProtectedRoute>
              }
            />
            <Route
              path="micro-finance-banks"
              element={
                <ProtectedRoute>
                  <Mfbs />
                </ProtectedRoute>
              }
            />
            <Route
              path="store"
              element={
                <ProtectedRoute>
                  <Store />
                </ProtectedRoute>
              }
            />
            <Route
              path="credit-score"
              element={
                <ProtectedRoute>
                  <CreditScorePage />
                </ProtectedRoute>
              }
            />
            <Route
              path="credit-score/credit-details"
              element={
                <ProtectedRoute>
                  <CreditDetails />
                </ProtectedRoute>
              }
            />
            <Route
              path="customer-care"
              element={
                <ProtectedRoute>
                  <CustomerCare />
                </ProtectedRoute>
              }
            />
            <Route
              path="customer-care/customer-chats"
              element={
                <ProtectedRoute>
                  <CustomerChat />
                </ProtectedRoute>
              }
            />
            <Route
              path="dietitians"
              element={
                <ProtectedRoute>
                  <Dietitian />
                </ProtectedRoute>
              }
            />
            <Route
              path="dietitians/dietitian-requests"
              element={<DietitianRequests />}
            />
            <Route
              path="staff"
              element={
                <ProtectedRoute>
                  <Staff />
                </ProtectedRoute>
              }
            />
            <Route
              path="operations"
              element={
                <ProtectedRoute>
                  <Operations />
                </ProtectedRoute>
              }
            />
            <Route
              path="restaurants"
              element={
                <ProtectedRoute>
                  <Restaurants />
                </ProtectedRoute>
              }
            />
            <Route
              path="restaurants/restaurant-information"
              element={<RestaurantInfo />}
            />
            <Route
              path="restaurants/add-new-restaurant"
              element={<AddRestaurant />}
            />
            <Route path="restaurants/settlement" element={<Settlement />} />
            <Route
              path="logistics"
              element={
                <ProtectedRoute>
                  <Logistics />
                </ProtectedRoute>
              }
            />
            <Route
              path="warehouse-management"
              element={
                <ProtectedRoute>
                  <WareHouse />
                </ProtectedRoute>
              }
            />
            <Route
              path="warehouse-management/:stateId"
              element={
                <ProtectedRoute>
                  <WareHouseList />
                </ProtectedRoute>
              }
            />
            <Route
              path="warehouse-management/add-store"
              element={
                <ProtectedRoute>
                  <AddStateForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="store-detail/:storeId"
              element={
                <ProtectedRoute>
                  <StoreDetail />
                </ProtectedRoute>
              }
            />
            <Route
              path="assignment"
              element={
                <ProtectedRoute>
                  <AssignmentIndex />
                </ProtectedRoute>
              }
            />
            <Route
              path="assignment/admin-assignment"
              element={
                <ProtectedRoute>
                  <AdminAssignment />
                </ProtectedRoute>
              }
            />
            <Route
              path="assignment/sales-admin-ministries"
              element={
                <ProtectedRoute>
                  <SalesAdminAssignment />
                </ProtectedRoute>
              }
            />
            <Route
              path="assignment/sales-admin-pages"
              element={
                <ProtectedRoute>
                  <SalesAdminPages />
                </ProtectedRoute>
              }
            />
            <Route
              path="document-check"
              element={
                <ProtectedRoute>
                  <DocumentCheck />
                </ProtectedRoute>
              }
            />

            <Route
              path="next-pay"
              element={
                <ProtectedRoute>
                  <NextPay />
                </ProtectedRoute>
              }
            />
            <Route
              path="defaulters"
              element={
                <ProtectedRoute>
                  <Defaulters />
                </ProtectedRoute>
              }
            />
            <Route
              path="blacklist"
              element={
                <ProtectedRoute>
                  <Blacklist />
                </ProtectedRoute>
              }
            />
            <Route
              path="vendors"
              element={
                <ProtectedRoute>
                  <Vendors />
                </ProtectedRoute>
              }
            />
            <Route path="vendors/vendor-information" element={<VendorInfo />} />
            <Route
              path="news-update"
              element={
                <ProtectedRoute>
                  <NewsUpdate />
                </ProtectedRoute>
              }
            />
            <Route
              path="faq"
              element={
                <ProtectedRoute>
                  <Faq />
                </ProtectedRoute>
              }
            />
            <Route
              path="repayment-history"
              element={
                <ProtectedRoute>
                  <Repayment />
                </ProtectedRoute>
              }
            />
            <Route
              path="user-category"
              element={
                <ProtectedRoute>
                  <UserCategory />
                </ProtectedRoute>
              }
            />
            <Route
              path="transactions"
              element={
                <ProtectedRoute>
                  <Transactions />
                </ProtectedRoute>
              }
            />
            <Route
              path="create-admin"
              element={
                <ProtectedRoute>
                  <CreateAdmin />
                </ProtectedRoute>
              }
            />
            <Route
              path="e-mandate"
              element={
                <ProtectedRoute>
                  <Emandate />
                </ProtectedRoute>
              }
            />
            <Route
              path="pending-emandate"
              element={
                <ProtectedRoute>
                  <PendingEmandates />
                </ProtectedRoute>
              }
            />
            <Route path="vendors/settlement" element={<VendorSettlement />} />
            <Route
              path="packages"
              element={
                <ProtectedRoute>
                  <Index />
                </ProtectedRoute>
              }
            />
            <Route
              path="all-canceled-mandates"
              element={
                <ProtectedRoute>
                  <AllCanceledMandates />
                </ProtectedRoute>
              }
            />

            <Route
              path="referrals"
              element={
                <ProtectedRoute>
                  <ReferralIndex />
                </ProtectedRoute>
              }
            />
            <Route
              path="referrals/referrer-details"
              element={
                <ProtectedRoute>
                  <ReferrerDetails />
                </ProtectedRoute>
              }
            />
            <Route
              path="referrals/sales-admin-referrals"
              element={
                <ProtectedRoute>
                  <SalesReferral />
                </ProtectedRoute>
              }
            />
            <Route
              path="referrals/sales-admin-referrals/details"
              element={
                <ProtectedRoute>
                  <SalesDetails />
                </ProtectedRoute>
              }
            />

            <Route
              path="logistics/rider-activity"
              element={<RiderActivity />}
            />
            <Route path="logistics/busy-riders" element={<BusyRiders />} />
            <Route
              path="logistics/assign-delivery"
              element={<AssignDelivery />}
            />
            <Route
              path="logistics/assigned-orders"
              element={<AssignedOrders />}
            />
            <Route
              path="logistics/all-deliveries"
              element={<AllDeliveries />}
            />
            <Route path="logistics/disbutes" element={<Disbutes />} />
            <Route path="logistics/add-rider" element={<AddRider />} />
          </Route>
        </Routes>
      </Router>
    </>
  );
}

export default App;
