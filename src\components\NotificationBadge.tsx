import React from 'react';
import { IoIosNotificationsOutline } from "react-icons/io";
import { Link } from "react-router-dom";
import { useAppSelector } from "../redux/hooks";
import { RootState } from "../redux/store";

const NotificationBadge: React.FC = () => {
  const { unreadCount, isConnected } = useAppSelector((state: RootState) => state.notifications);

  return (
    <div className="relative">
      <Link to="notifications">
        <div className="relative">
          <IoIosNotificationsOutline className="w-8 h-8" />
          {unreadCount > 0 && (
            <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">
              {unreadCount > 99 ? '99+' : unreadCount}
            </span>
          )}
          {/* Connection status indicator */}
          <span 
            className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${
              isConnected ? 'bg-green-500' : 'bg-gray-400'
            }`}
            title={isConnected ? 'Connected to real-time notifications' : 'Disconnected'}
          />
        </div>
      </Link>
    </div>
  );
};

export default NotificationBadge;
