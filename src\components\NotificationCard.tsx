import { IoIosMore, IoMdCheckmark } from "react-icons/io";

const NotificationCard = ({
  index,
  img,
  name,
  msg,
  dateTime,
  readStatus,
  setReadMsg,
  setUnreadMsg,
  activeNotificationId,
  setActiveNotificationId,
}: {
  index: number;
  img: string;
  name: string;
  msg: string;
  dateTime: string;
  readStatus: boolean;
  setReadMsg: () => void;
  setUnreadMsg: () => void;
  activeNotificationId: null | number;
  setActiveNotificationId: (index: number | null) => void;
}) => {
  const isActive = activeNotificationId === index;
  const handleOutsideClick = () => setActiveNotificationId(null);

  return (
    <section
      className={`${
        readStatus ? "bg-white" : "bg-gray-50"
      } my-1 flex items-center justify-between p-4 border-b hover:bg-gray-50 transition-colors relative`}
      onClick={handleOutsideClick}
    >
      <section
        className="flex items-center gap-4 cursor-pointer flex-1"
        onClick={setReadMsg}
      >
        <div className="relative">
          <figure className="w-12 h-12">
            <img src={img} alt={name} className="w-full h-full rounded-full object-cover" />
          </figure>
          {/* Unread indicator */}
          {!readStatus && (
            <div className="absolute -top-1 -left-1 w-3 h-3 rounded-full bg-blue-500"></div>
          )}
        </div>
        <div className="flex-1">
          <div className="flex items-center gap-2">
            <h5 className="font-semibold text-gray-900 text-sm">{name}</h5>
            <span className="text-gray-600 text-sm">{msg}</span>
          </div>
          <p className="text-gray-500 text-xs mt-1">{dateTime}</p>
        </div>
      </section>
      <section className="flex items-center gap-2">
        <button
          type="button"
          onClick={(e) => {
            e.stopPropagation();
            setActiveNotificationId(isActive ? null : index);
          }}
          className="p-1 hover:bg-gray-200 rounded-full transition-colors"
        >
          <IoIosMore className="w-5 h-5 text-gray-400" />
        </button>
      </section>
      {isActive && (
        <section className="p-2 shadow-md absolute bg-white right-10 z-10 rounded-md -bottom-24">
          <ul className="text-sm">
            <li className="p-2">
              {readStatus ? (
                <button
                  type="button"
                  className="flex items-center gap-3 w-full group"
                  onClick={() => {
                    setUnreadMsg();
                    setActiveNotificationId(null);
                  }}
                >
                  Mark as unread
                  <IoMdCheckmark className="text-gray-500 opacity-0 group-hover:opacity-100 group-hover:text-secondary transition-opacity duration-200" />
                </button>
              ) : (
                <button
                  type="button"
                  className="flex items-center gap-3 w-full group"
                  onClick={() => {
                    setReadMsg();
                    setActiveNotificationId(null);
                  }}
                >
                  Mark as read
                  <IoMdCheckmark className="text-gray-500 opacity-0 group-hover:opacity-100 group-hover:text-secondary transition-opacity duration-200" />
                </button>
              )}
            </li>
            <li className="p-2">Archive </li>
            <li className="p-2">Turn off notifications</li>
          </ul>
        </section>
      )}
    </section>
  );
};

export default NotificationCard;
