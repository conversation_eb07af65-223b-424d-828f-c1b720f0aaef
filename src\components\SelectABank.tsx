import { useState, useEffect } from "react";
import axios from "axios";
import toast from "react-hot-toast";
import Modal, { ModalContent } from "./elements/Modal";

const SelectABank = ({ toggleModal, setBank }: any) => {
  const [bankList, setBankList] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    fetchBanks();
  }, []);

  const fetchBanks = async () => {
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/getAllBanks`
      );

      if (response.data && response.data.data) {
        setBankList(response.data.data);
      } else {
        setBankList([]);
      }
    } catch (error: any) {
      toast.error("Error fetching banks");
    }
  };

  const handleBankChange = (value: string) => {
    const selectedBank = bankList.find(
      (bankItem: { bank_name: string }) => bankItem.bank_name === value
    );

    if (selectedBank) {
      setBank(selectedBank);
    }
    toggleModal();
  };

  const handleSearchChange = (text: string) => {
    setSearchQuery(text);
  };

  return (
    <Modal
      open={true}
      onClose={toggleModal}
      className="flex items-center justify-center"
    >
      <ModalContent className="mx-3 p-6 rounded-md shadow-lg flex flex-col justify-center items-center bg-white w-[80%] md:w-[45%] lg:w-[35%] xl:w-[25%] ">
        <h1 className="font-semibold text-base pb-6">Select Bank</h1>
        <input
          placeholder="Search for bank"
          onChange={(e) => handleSearchChange(e.target.value)}
          value={searchQuery}
          className="rounded-tl-2xl w-[90%] rounded-br-2xl indent-2 focus:outline-[#008B50] text-xs font-medium border my-1 p-3"
        />
        <ul className="scrollable-list rounded w-[90%] border my-1 p-3 h-48 overflow-y-auto">
          {bankList
            .filter((bankItem: { bank_name: string }) =>
              bankItem.bank_name
                .toLowerCase()
                .includes(searchQuery.toLowerCase())
            )
            .map((filteredBankItem: { bank_name: string }, idx: number) => (
              <li
                key={idx}
                className="cursor-pointer p-2 hover:bg-gray-200 text-xs"
                onClick={() => handleBankChange(filteredBankItem.bank_name)}
              >
                {filteredBankItem.bank_name}
              </li>
            ))}
        </ul>
      </ModalContent>
    </Modal>
  );
};

export default SelectABank;
