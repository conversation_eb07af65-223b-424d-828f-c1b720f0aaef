import { MdOutlineCancel } from "react-icons/md";
import Modal, { ModalContent } from "../elements/Modal";
import { useState } from "react";
import axios from "axios";
import { useAppSelector } from "../../redux/hooks";
import toast from "react-hot-toast";
import LoadingSpinner from "../elements/LoadingSpinner";

const AddReferralModal = ({ closeModal, reloadReferralPage }: any) => {
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState("");
  const { token } = useAppSelector((store) => store.auth);

  const handleAddReferral = async () => {
    setIsLoading(true);
    if (!email) {
      toast.error("Email is required");
      setIsLoading(false);
      return;
    }
    try {
      const res = await axios.post(
        `${process.env.REACT_APP_API_URL}/createReferralList`,
        {
          email,
        },
        {
          headers: {
            Authorization: token,
          },
        }
      );

      toast.success(res.data.message || "Referral added successfully");
      reloadReferralPage();
      closeModal();
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to add referral");
      console.error("Error adding referral:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      open={true}
      onClose={closeModal}
      className="flex items-center justify-center"
    >
      <ModalContent className="mx-3 p-6 rounded-md shadow-lg flex flex-col justify-center items-center bg-white w-[80%] md:w-[55%] lg:w-[50%] xl:w-[30%] ">
        <section className="w-full p-2">
          <div className="flex justify-end">
            <MdOutlineCancel
              className="text-2xl cursor-pointer"
              onClick={closeModal}
            />
          </div>

          <section>
            <h3 className="font-semibold">Enter Email of Sales User</h3>

            <input
              type="email"
              placeholder="Enter email"
              className="border border-gray-300 text-sm rounded-md p-2 w-full mt-2"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
            <button
              type="submit"
              className=" bg-secondary text-white text-sm px-4 py-2 rounded-md mt-4 w-40"
              onClick={handleAddReferral}
            >
              {isLoading ? <LoadingSpinner /> : "Add Referrer"}
            </button>
          </section>
        </section>
      </ModalContent>
    </Modal>
  );
};

export default AddReferralModal;
