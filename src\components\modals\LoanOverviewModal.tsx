import { FaTimes } from "react-icons/fa";

interface LoanOverviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  userData: {
    firstName: string;
    lastName: string;
    loanInitialDate: string;
    nextDueDate: string;
    firstInstalment: number;
    secondInstallment: number;
  };
}

const LoanOverviewModal = ({ isOpen, onClose, userData }: LoanOverviewModalProps) => {
  if (!isOpen) return null;

  const formatDate = (dateStr: string): string => {
    try {
      const date = new Date(dateStr);
      const options: Intl.DateTimeFormatOptions = {
        year: "numeric",
        month: "long",
        day: "numeric",
      };
      return date.toLocaleDateString("en-US", options);
    } catch (e) {
      return "Invalid Date";
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 2,
    }).format(amount).replace('NGN', '₦');
  };

  const calculateDaysLeft = (dueDate: string): number => {
    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = due.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const daysLeft = calculateDaysLeft(userData.nextDueDate);
  const totalOwed = userData.firstInstalment + userData.secondInstallment;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-800">Loan Overview:</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <FaTimes className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Date of Accrual */}
          <div className="flex items-center justify-between">
            <span className="text-gray-700 font-medium">Date of Accrual:</span>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-green-500 rounded"></div>
              <span className="text-gray-600">{formatDate(userData.loanInitialDate)}</span>
            </div>
          </div>

          {/* Amount Owed */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-gray-700 font-medium">Amount Owed as of today:</span>
              <span className="text-2xl font-bold text-gray-800">{formatCurrency(totalOwed)}</span>
            </div>
            <div className="text-right">
              <span className={`text-sm ${daysLeft > 0 ? 'text-red-500' : 'text-green-500'}`}>
                {daysLeft > 0 ? `Due on ${formatDate(userData.nextDueDate)} - ${daysLeft} days left` : 'Overdue'}
              </span>
            </div>
          </div>

          {/* Installments */}
          <div className="space-y-4">
            {/* First Installment */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-gray-700 font-medium">First Installment:</span>
                <div className="flex items-center justify-center w-8 h-8 bg-green-500 text-white rounded-full text-sm font-bold">
                  1/2
                </div>
              </div>
              <span className="text-xl font-semibold text-gray-800">
                {userData.firstInstalment === 0 ? '₦ 0' : formatCurrency(userData.firstInstalment)}
              </span>
            </div>

            {/* Second Installment */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-gray-700 font-medium">Second Installment:</span>
                <div className="flex items-center justify-center w-8 h-8 bg-green-500 text-white rounded-full text-sm font-bold">
                  1/2
                </div>
              </div>
              <span className="text-xl font-semibold text-gray-800">
                {formatCurrency(userData.secondInstallment)}
              </span>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t">
          <button
            onClick={onClose}
            className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors font-medium"
          >
            Back
          </button>
        </div>
      </div>
    </div>
  );
};

export default LoanOverviewModal;
