import { useState } from "react";
import axios from "axios";
import { useAppDispatch, useAppSelector } from "../../redux/hooks";
import { RootState } from "../../redux/store";
import toast from "react-hot-toast";
import { Preloader } from "../elements/Preloader";
import { getFees } from "../../redux/thunk";

const ServiceModal = ({
  setShowServiceModal,
  feesDetails,
  setFeesDetails,
}: any) => {
  const { token } = useAppSelector((store: RootState) => store.auth);
  const [serviceFee, setServiceFee] = useState("");
  const [deliveryFee, setDeliveryFee] = useState("");
  const [interestRate, setInterestRate] = useState("");
  const [stateLocation, setStateLocation] = useState("");
  const [increment, setIncrement] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useAppDispatch();

  const handleSubmit = async () => {
    const payload = {
      serviceFee: serviceFee || feesDetails.serviceFee,
      deliveryFee: deliveryFee || feesDetails.deliveryFee,
      interest: interestRate || feesDetails.interest,
      stateLocation: stateLocation || feesDetails.stateLocation,
      increment: increment || feesDetails.increment,
    };

    const url = feesDetails._id
      ? axios.put(
          `${process.env.REACT_APP_API_URL}/updateDeliveryService/${feesDetails._id}`,
          payload,
          {
            headers: {
              Authorization: token,
            },
          }
        )
      : axios.post(`${process.env.REACT_APP_API_URL}/createFees`, payload, {
          headers: {
            Authorization: token,
          },
        });

    setIsLoading(true);
    try {
      const res = await url;
      setIsLoading(false);

      dispatch(getFees());
      setServiceFee("");
      setDeliveryFee("");
      setInterestRate("");
      setStateLocation("");
      setIncrement("");
      setFeesDetails({});
      setShowServiceModal(false);
      toast.success(res.data.message || "Updated successfully");
    } catch (error: any) {
      setIsLoading(false);
      toast.error(error.message || "Failed to update");
    }
  };

  return (
    <div className="fixed z-40 inset-0 flex items-center justify-center bg-black bg-opacity-60">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-8 relative">
        {/* Close Button */}
        <button
          onClick={() => {
            setFeesDetails({});
            setShowServiceModal(false);
          }}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-700 transition"
          aria-label="Close"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>

        <h2 className="text-2xl font-semibold text-gray-800 mb-6 text-center">
          {feesDetails._id ? "Edit" : "Set"} Service & Delivery Fee
        </h2>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            handleSubmit();
          }}
          className="space-y-5"
        >
          <div>
            <label
              htmlFor="service-fee"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Service Fee
            </label>
            <input
              className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-secondary"
              type="number"
              name="service-fee"
              id="service-fee"
              value={
                serviceFee !== ""
                  ? serviceFee
                  : feesDetails.serviceFee
                  ? feesDetails.serviceFee
                  : serviceFee
              }
              onChange={(e) => setServiceFee(e.target.value)}
              min={0}
              required
            />
          </div>
          <div>
            <label
              htmlFor="delivery-fee"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Delivery Fee
            </label>
            <input
              className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-secondary"
              type="number"
              name="delivery-fee"
              id="delivery-fee"
              value={
                deliveryFee !== ""
                  ? deliveryFee
                  : feesDetails.deliveryFee
                  ? feesDetails.deliveryFee
                  : deliveryFee
              }
              onChange={(e) => setDeliveryFee(e.target.value)}
              min={0}
              required
            />
          </div>
          <div>
            <label
              htmlFor="interestRate"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Interest Rate
            </label>
            <input
              className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-secondary"
              type="number"
              name="interestRate"
              id="interestRate"
              value={
                interestRate !== ""
                  ? interestRate
                  : feesDetails.interest
                  ? feesDetails.interest
                  : interestRate
              }
              onChange={(e) => setInterestRate(e.target.value)}
              min={0}
              required
            />
          </div>
          <div>
            <label
              htmlFor="location"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Location
            </label>
            <input
              className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-secondary"
              type="text"
              name="location"
              id="location"
              placeholder="e.g. Oyo, Lagos"
              value={
                stateLocation !== ""
                  ? stateLocation
                  : feesDetails.stateLocation
                  ? feesDetails.stateLocation
                  : stateLocation
              }
              onChange={(e) => setStateLocation(e.target.value)}
              required
            />
          </div>
          <div>
            <label
              htmlFor="increment"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Increment
            </label>
            <input
              className="w-full border border-gray-300 rounded px-3 py-2"
              type="number"
              name="increment"
              id="increment"
              min={1}
              value={
                increment !== ""
                  ? increment
                  : feesDetails.increment
                  ? feesDetails.increment
                  : increment
              }
              onChange={(e) => setIncrement(e.target.value)}
            />
          </div>
          <div className="pt-2">
            <button
              type="submit"
              className="w-full flex justify-center items-center px-4 py-2 rounded bg-secondary text-white font-semibold hover:bg-secondary-dark transition"
              disabled={isLoading}
            >
              {isLoading ? <Preloader /> : "Submit"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ServiceModal;
