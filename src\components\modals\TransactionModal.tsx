import { FaTimes } from "react-icons/fa";

interface TransactionDetails {
  _id: string;
  transactionReference: string;
  amount: number;
  eventType: string;
  message: string;
  createdAt: string;
}

interface UserDetails {
  _id: string;
  email: string;
  virtualAccount: {
    walletbalance: number;
  };
  firstName: string;
  lastName: string;
  accountType?: string;
  categoryType?: string;
}

interface TransactionModalProps {
  userDetails: UserDetails;
  transactionDetails: TransactionDetails[];
  walletBalance: number;
  onClose: () => void;
}

const TransactionModal = ({ userDetails, transactionDetails, walletBalance, onClose }: TransactionModalProps) => {
  const getStatusColor = (eventType: string) => {
    switch (eventType.toLowerCase()) {
      case "repayment":
        return "text-green-600";
      case "loan":
        return "text-blue-600";
      case "credit":
        return "text-green-600";
      case "debit":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString().slice(-2);
    return `${day}-${month}-${year}`;
  };

  const getStatusText = (eventType: string) => {
    switch (eventType.toLowerCase()) {
      case "repayment":
        return "Successful";
      default:
        return eventType;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-2">
            <h2 className="text-xl font-semibold text-gray-800">Transactions</h2>
            <span className="text-gray-500">-</span>
            <div className="flex items-center gap-2">
              <span className="text-green-600 font-medium">Wallet Balance:</span>
              <span className="text-xl font-bold text-gray-800">
                ₦ {walletBalance.toLocaleString()}
              </span>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <FaTimes className="w-5 h-5" />
          </button>
        </div>

        {/* User Information */}
        <div className="p-6 border-b bg-gray-50">
          <h3 className="text-lg font-medium text-gray-800 mb-2">
            {userDetails.firstName} {userDetails.lastName}
          </h3>
          <p className="text-gray-600">{userDetails.email}</p>
        </div>

        {/* Transaction Details */}
        <div className="p-6 space-y-6 max-h-96 overflow-y-auto">
          {transactionDetails.map((transaction, index) => (
            <div key={transaction._id}>
              <div className="space-y-3">
                <div className="flex justify-between items-start">
                  <span className="text-gray-700 font-medium">Transaction Type:</span>
                  <span className="text-gray-600 text-right capitalize">{transaction.eventType}</span>
                </div>

                <div className="flex justify-between items-start">
                  <span className="text-gray-700 font-medium">Date:</span>
                  <span className="text-gray-600">{formatDate(transaction.createdAt)}</span>
                </div>

                <div className="flex justify-between items-start">
                  <span className="text-gray-700 font-medium">Amount:</span>
                  <span className="text-gray-600 font-semibold">
                    ₦ {transaction.amount.toLocaleString()}
                  </span>
                </div>

                <div className="flex justify-between items-start">
                  <span className="text-gray-700 font-medium">Description:</span>
                  <span className="text-gray-600 text-right max-w-[300px]">
                    {transaction.message}
                  </span>
                </div>

                <div className="flex justify-between items-start">
                  <span className="text-orange-500 font-medium">Status Of Transaction:</span>
                  <span className={`font-medium ${getStatusColor(transaction.eventType)}`}>
                    {getStatusText(transaction.eventType)}
                  </span>
                </div>

                <div className="flex justify-between items-start">
                  <span className="text-gray-700 font-medium">Reference:</span>
                  <span className="text-gray-600 text-sm font-mono">
                    {transaction.transactionReference}
                  </span>
                </div>
              </div>

              {/* Divider - only show if not the last item */}
              {index < transactionDetails.length - 1 && (
                <hr className="border-gray-200 mt-6" />
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TransactionModal;
