import { FaTimes, FaUser, FaEnvelope, FaPhone, FaWallet, FaCalendar, FaCreditCard } from "react-icons/fa";

interface ViewBlacklistModalProps {
  isOpen: boolean;
  onClose: () => void;
  userData: {
    _id: string;
    customerId: {
      _id: string;
      email: string;
      phoneNumber: string;
      firstName: string;
      lastName: string;
      categoryType: string;
      virtualAccount: {
        walletbalance: number;
      };
    };
    walletId: {
      _id: string;
      loanLimit: number;
      creditLimit: number;
      lastLoanRecord: {
        orderAmount: number;
        totalLoan: number;
        paidLoan: number;
        amountLeft: number;
        montlyPayBack: number;
        payLeft: number;
        loanInitialDate: string;
        payDate: string;
        loanDate: string;
      };
    };
    createdAt: string;
    updatedAt: string;
  };
}

const ViewBlacklistModal = ({ isOpen, onClose, userData }: ViewBlacklistModalProps) => {
  if (!isOpen) return null;

  const formatDate = (dateStr: string): string => {
    try {
      const date = new Date(dateStr);
      const options: Intl.DateTimeFormatOptions = {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      };
      return date.toLocaleDateString("en-US", options);
    } catch (e) {
      return "Invalid Date";
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 2,
    }).format(amount).replace('NGN', '₦');
  };

  const { customerId, walletId } = userData;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-red-100 rounded-lg">
              <FaUser className="w-5 h-5 text-red-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-800">Blacklisted User Details</h2>
              <p className="text-sm text-gray-600">Complete information about the blacklisted user</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <FaTimes className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* User Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-800 mb-4 flex items-center gap-2">
              <FaUser className="w-4 h-4" />
              Personal Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Full Name</label>
                <p className="text-sm text-gray-900 font-medium">
                  {customerId.firstName} {customerId.lastName}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">User ID</label>
                <p className="text-sm text-gray-900 font-mono">{customerId._id}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600 mb-1 flex items-center gap-1">
                  <FaEnvelope className="w-3 h-3" />
                  Email Address
                </label>
                <p className="text-sm text-gray-900">{customerId.email}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600 mb-1 flex items-center gap-1">
                  <FaPhone className="w-3 h-3" />
                  Phone Number
                </label>
                <p className="text-sm text-gray-900">{customerId.phoneNumber}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Category Type</label>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 capitalize">
                  {customerId.categoryType}
                </span>
              </div>
            </div>
          </div>

          {/* Wallet Information */}
          <div className="bg-green-50 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-800 mb-4 flex items-center gap-2">
              <FaWallet className="w-4 h-4" />
              Wallet & Credit Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Wallet Balance</label>
                <p className="text-lg font-bold text-green-600">
                  {formatCurrency(customerId.virtualAccount.walletbalance)}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Credit Limit</label>
                <p className="text-lg font-bold text-blue-600">
                  {formatCurrency(walletId.creditLimit)}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Loan Limit</label>
                <p className="text-lg font-bold text-purple-600">
                  {formatCurrency(walletId.loanLimit)}
                </p>
              </div>
            </div>
          </div>

          {/* Loan Information */}
          {walletId.lastLoanRecord && (
            <div className="bg-orange-50 rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-800 mb-4 flex items-center gap-2">
                <FaCreditCard className="w-4 h-4" />
                Last Loan Record
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Total Loan</label>
                  <p className="text-sm font-bold text-orange-600">
                    {formatCurrency(walletId.lastLoanRecord.totalLoan)}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Paid Loan</label>
                  <p className="text-sm font-bold text-green-600">
                    {formatCurrency(walletId.lastLoanRecord.paidLoan)}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Amount Left</label>
                  <p className="text-sm font-bold text-red-600">
                    {formatCurrency(walletId.lastLoanRecord.amountLeft)}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Monthly Payback</label>
                  <p className="text-sm font-bold text-blue-600">
                    {formatCurrency(walletId.lastLoanRecord.montlyPayBack)}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Loan Initial Date</label>
                  <p className="text-sm text-gray-900">
                    {formatDate(walletId.lastLoanRecord.loanInitialDate)}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">Last Pay Date</label>
                  <p className="text-sm text-gray-900">
                    {formatDate(walletId.lastLoanRecord.payDate)}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Blacklist Information */}
          <div className="bg-red-50 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-800 mb-4 flex items-center gap-2">
              <FaCalendar className="w-4 h-4" />
              Blacklist Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Blacklisted Date</label>
                <p className="text-sm text-gray-900">{formatDate(userData.createdAt)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Last Updated</label>
                <p className="text-sm text-gray-900">{formatDate(userData.updatedAt)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Blacklist ID</label>
                <p className="text-sm text-gray-900 font-mono">{userData._id}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">Status</label>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  Blacklisted
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t bg-gray-50">
          <button
            onClick={onClose}
            className="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors font-medium"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ViewBlacklistModal;
