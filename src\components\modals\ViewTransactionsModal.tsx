import { useState, useEffect } from "react";
import {
  FaTimes,
  FaHistory,
  FaCalendar,
  FaMoneyBillWave,
  FaChartLine,
  FaExclamationCircle,
} from "react-icons/fa";
import { useAppDispatch } from "../../redux/hooks";
import { getBlacklistHistoryByEmail } from "../../redux/thunk";
import toast from "react-hot-toast";

interface BlacklistHistoryRecord {
  _id: string;
  mandate: string;
  status: string;
  message: string;
  totalLoan: number;
  amountDebited: number;
  amountLeft: number;
  createdAt: string;
}

interface BlacklistHistoryResponse {
  message: string;
  history: BlacklistHistoryRecord[];
  isFullyPaid: boolean;
  lastDebitedAt: string;
}

interface ViewTransactionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  userData: {
    customerId: {
      _id: string;
      firstName: string;
      lastName: string;
      email: string;
    };
  };
}

const ViewTransactionsModal = ({
  isOpen,
  onClose,
  userData,
}: ViewTransactionsModalProps) => {
  const dispatch = useAppDispatch();
  const [historyData, setHistoryData] =
    useState<BlacklistHistoryResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const formatDate = (dateStr: string): string => {
    try {
      const date = new Date(dateStr);
      const options: Intl.DateTimeFormatOptions = {
        year: "numeric",
        month: "short",
        day: "numeric",
      };
      return date.toLocaleDateString("en-US", options);
    } catch (e) {
      return "Invalid Date";
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat("en-NG", {
      style: "currency",
      currency: "NGN",
      minimumFractionDigits: 2,
    })
      .format(amount)
      .replace("NGN", "₦");
  };

  useEffect(() => {
    const fetchBlacklistHistory = async () => {
      if (isOpen && userData.customerId.email) {
        setIsLoading(true);
        setError(null);
        setHistoryData(null);

        try {
          const response = await dispatch(
            getBlacklistHistoryByEmail({ email: userData.customerId.email })
          ).unwrap();
          setHistoryData(response);
        } catch (error: any) {
          console.error("Failed to fetch blacklist history:", error);
          setError(error || "Failed to fetch blacklist history");
          toast.error(error || "Failed to fetch blacklist history");
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchBlacklistHistory();
  }, [isOpen, userData.customerId.email, dispatch]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <FaHistory className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-800">
                Blacklist Payment History
              </h2>
              <p className="text-sm text-gray-600">
                {userData.customerId.firstName} {userData.customerId.lastName} (
                {userData.customerId.email})
              </p>
              {historyData?.lastDebitedAt && (
                <p className="text-xs text-gray-500 mt-1">
                  Last debited: {formatDate(historyData.lastDebitedAt)}
                </p>
              )}
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <FaTimes className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Loading blacklist history...</p>
              </div>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <FaExclamationCircle className="w-16 h-16 text-red-300 mx-auto mb-4" />
              <p className="text-red-500 text-lg mb-2">
                Failed to load history
              </p>
              <p className="text-gray-400 text-sm mb-4">{error}</p>
              <button
                onClick={() => {
                  setError(null);
                  // Trigger refetch by calling the effect again
                  const fetchBlacklistHistory = async () => {
                    if (userData.customerId.email) {
                      setIsLoading(true);
                      setError(null);
                      setHistoryData(null);

                      try {
                        const response = await dispatch(
                          getBlacklistHistoryByEmail({
                            email: userData.customerId.email,
                          })
                        ).unwrap();
                        setHistoryData(response);
                      } catch (error: any) {
                        console.error(
                          "Failed to fetch blacklist history:",
                          error
                        );
                        setError(error || "Failed to fetch blacklist history");
                        toast.error(
                          error || "Failed to fetch blacklist history"
                        );
                      } finally {
                        setIsLoading(false);
                      }
                    }
                  };
                  fetchBlacklistHistory();
                }}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors"
              >
                Retry
              </button>
            </div>
          ) : !historyData || historyData.history.length === 0 ? (
            <div className="text-center py-12">
              <FaHistory className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500 text-lg">
                No blacklist history found
              </p>
              <p className="text-gray-400 text-sm">
                This user has no recorded blacklist payment history.
              </p>
            </div>
          ) : (
            <>
              {/* Summary Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <FaChartLine className="w-5 h-5 text-blue-600" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-600">
                        Total Records
                      </p>
                      <p className="text-2xl font-bold text-blue-600">
                        {historyData.history.length}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <FaMoneyBillWave className="w-5 h-5 text-green-600" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-600">
                        Total Debited
                      </p>
                      <p className="text-lg font-bold text-green-600">
                        {formatCurrency(
                          historyData.history.reduce(
                            (sum, t) => sum + t.amountDebited,
                            0
                          )
                        )}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="bg-red-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <div className="p-2 bg-red-100 rounded-lg">
                      <FaMoneyBillWave className="w-5 h-5 text-red-600" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-600">
                        Amount Left
                      </p>
                      <p className="text-lg font-bold text-red-600">
                        {formatCurrency(
                          historyData.history[0]?.amountLeft || 0
                        )}
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  className={`p-4 rounded-lg ${
                    historyData.isFullyPaid ? "bg-green-50" : "bg-orange-50"
                  }`}
                >
                  <div className="flex items-center">
                    <div
                      className={`p-2 rounded-lg ${
                        historyData.isFullyPaid
                          ? "bg-green-100"
                          : "bg-orange-100"
                      }`}
                    >
                      <FaChartLine
                        className={`w-5 h-5 ${
                          historyData.isFullyPaid
                            ? "text-green-600"
                            : "text-orange-600"
                        }`}
                      />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-600">
                        Payment Status
                      </p>
                      <p
                        className={`text-lg font-bold ${
                          historyData.isFullyPaid
                            ? "text-green-600"
                            : "text-orange-600"
                        }`}
                      >
                        {historyData.isFullyPaid ? "Fully Paid" : "Pending"}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Blacklist History Table */}
              <div className="overflow-x-auto">
                <table className="w-full" style={{ minWidth: "1000px" }}>
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="p-3 text-left text-sm font-medium text-gray-700">
                        S/N
                      </th>
                      <th className="p-3 text-left text-sm font-medium text-gray-700">
                        Date
                      </th>
                      <th className="p-3 text-left text-sm font-medium text-gray-700">
                        Mandate ID
                      </th>
                      <th className="p-3 text-left text-sm font-medium text-gray-700">
                        Status
                      </th>
                      <th className="p-3 text-left text-sm font-medium text-gray-700">
                        Message
                      </th>
                      <th className="p-3 text-left text-sm font-medium text-gray-700">
                        Total Loan
                      </th>
                      <th className="p-3 text-left text-sm font-medium text-gray-700">
                        Amount Debited
                      </th>
                      <th className="p-3 text-left text-sm font-medium text-gray-700">
                        Amount Left
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {historyData.history.map((record, index) => (
                      <tr key={record._id} className="hover:bg-gray-50">
                        <td className="p-3 text-sm text-gray-900">
                          {String(index + 1).padStart(3, "0")}
                        </td>
                        <td className="p-3 text-sm text-gray-900">
                          <div className="flex items-center gap-2">
                            <FaCalendar className="w-3 h-3 text-gray-400" />
                            {formatDate(record.createdAt)}
                          </div>
                        </td>
                        <td className="p-3 text-sm text-gray-900">
                          <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                            {record.mandate.substring(0, 8)}...
                          </span>
                        </td>
                        <td className="p-3 text-sm">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              record.status === "failed"
                                ? "bg-red-100 text-red-800"
                                : record.status === "success"
                                ? "bg-green-100 text-green-800"
                                : "bg-yellow-100 text-yellow-800"
                            }`}
                          >
                            {record.status.charAt(0).toUpperCase() +
                              record.status.slice(1)}
                          </span>
                        </td>
                        <td className="p-3 text-sm text-gray-900 max-w-xs">
                          <div className="truncate" title={record.message}>
                            {record.message}
                          </div>
                        </td>
                        <td className="p-3 text-sm">
                          <span className="font-semibold text-blue-600">
                            {formatCurrency(record.totalLoan)}
                          </span>
                        </td>
                        <td className="p-3 text-sm">
                          <span className="font-semibold text-green-600">
                            {formatCurrency(record.amountDebited)}
                          </span>
                        </td>
                        <td className="p-3 text-sm">
                          <span className="font-semibold text-orange-600">
                            {formatCurrency(record.amountLeft)}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t bg-gray-50">
          <button
            onClick={onClose}
            className="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors font-medium"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ViewTransactionsModal;
