import { BiSearchAlt } from "react-icons/bi";

const InactiveComponent = ({
  setSelectedData,
  setSelectedSubdata,
  selectedData,
  data,
  setShowCreateModal,
  setCreateMinistry,
  setCategoryId,
  setSubCategoryId,
  path,
}: {
  setSelectedData: any;
  setSelectedSubdata: any;
  setSubCategoryId: any;
  setCategoryId: any;
  createMinistry: boolean;
  selectedData: {
    category: string;
    categoryIcon: string;
    categoryType?: [
      {
        name: string;
        _id: string;
      }
    ];
    _id?: string;
  };
  data: any[];
  setShowCreateModal: (prev: boolean) => void;
  setCreateMinistry: (prev: boolean) => void;
  path: string;
}) => {
  const adminLevel = sessionStorage.getItem("adminLevel");
  const subData = selectedData.categoryType;

  return (
    <section>
      {adminLevel === "superadmin" && path === "stateGovernment" && (
        <section className="flex justify-end p-2 gap-3">
          <button
            type="button"
            className="bg-secondary text-xs text-white p-2 rounded-sm font-sans"
            onClick={() => {
              setShowCreateModal(true);
              setCreateMinistry(true);
            }}
          >
            Create Ministry
          </button>

          <button
            type="button"
            className="bg-secondary text-xs text-white p-2 rounded-sm font-sans"
            onClick={() => setShowCreateModal(true)}
          >
            Create State
          </button>
        </section>
      )}

      <section className="text-center text-sm shadow-md p-2 pb-4 font-mont font-medium w-fit">
        <h4>Select {path === "stateGovernment" ? "State" : ""}</h4>
        <div className="relative my-3">
          <BiSearchAlt className="absolute w-5 h-5 opacity-70 top-2 left-2" />
          <input
            type="search"
            name="search"
            id="search"
            placeholder="Search"
            className="border rounded-md p-2 indent-5 w-full"
          />
        </div>
        <section>
          {data ? (
            data.map((item, index) => (
              <section
                key={index}
                className="flex items-center justify-between border-b-2 p-2 cursor-pointer relative"
                onClick={() => {
                  setSelectedData(item);
                  setCategoryId(item._id);
                }}
              >
                <div className="flex gap-3 items-center">
                  <img
                    src={item.categoryIcon}
                    alt={item.category}
                    className="w-10 h-10 bg-cover rounded-[50%]"
                  />
                  <p>{item.category}</p>
                </div>
              </section>
            ))
          ) : (
            <p>Nothing to show</p>
          )}
          <section
            className={`${
              selectedData.category ? "block" : "hidden"
            } text-center text-sm shadow-lg p-4 font-mont font-medium absolute top-1/3 left-[30rem] bg-white w-80 max-h-96 rounded-lg border z-10`}
          >
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-semibold">
                Select {path === "stateGovernment" ? "Ministry" : "Bank"}
              </h4>
              <span className="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full font-medium">
                {subData ? subData.length : 0}{" "}
                {path === "stateGovernment" ? "Ministries" : "Banks"}
              </span>
            </div>

            <div className="relative mb-3">
              <BiSearchAlt className="absolute w-5 h-5 opacity-70 top-2 left-2" />
              <input
                type="search"
                name="search"
                id="search"
                placeholder="Search"
                className="border rounded-md p-2 indent-5 w-full"
              />
            </div>

            <section className="max-h-64 overflow-y-auto scroll-bar">
              {subData && subData.length > 0 ? (
                subData.map((item: any, index: any) => (
                  <section
                    key={index}
                    className="flex items-center justify-between border-b p-3 cursor-pointer hover:bg-gray-50 transition-colors"
                    onClick={() => {
                      const radio = document.getElementById(
                        `radio-${index}`
                      ) as HTMLInputElement;
                      if (radio) {
                        radio.checked = true;
                      }
                      setSelectedSubdata(item.name);
                      setSubCategoryId(item._id);
                    }}
                  >
                    <div className="flex gap-3 items-center">
                      <p className="text-left">{item.name}</p>
                    </div>
                    <input
                      type="radio"
                      name="state"
                      id={`radio-${index}`}
                      className="peer hidden"
                    />
                    <label
                      htmlFor={`radio-${index}`}
                      className="w-5 h-5 items-center justify-center hidden peer-checked:flex border rounded-full peer-checked:bg-secondary peer-checked:text-white"
                    >
                      ✔
                    </label>
                  </section>
                ))
              ) : (
                <div className="text-gray-500 py-4">
                  No {path === "stateGovernment" ? "ministries" : "banks"}{" "}
                  available
                </div>
              )}
            </section>
          </section>
        </section>
      </section>
    </section>
  );
};

export default InactiveComponent;
