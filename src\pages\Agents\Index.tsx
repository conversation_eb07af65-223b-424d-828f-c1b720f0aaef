import axios from "axios";
import { useEffect, useState } from "react";
import LoadingSpinner from "../../components/elements/LoadingSpinner";
import Pagination from "../../components/Pagination";
import { useAppSelector } from "../../redux/hooks";
import toast from "react-hot-toast";
import UpdateTarget from "./UpdateTarget";
import UpdateEarnings from "./UpdateEarnings";
import ShowHistory from "./ShowHistory";
import ShowAccount from "./ShowAccount";

interface Agent {
  _id: string;
  firstname: string;
  lastname: string;
  email: string;
  category: string;
  referralCode: string;
  target: number;
  expectedEarnings: number;
}

const Index = () => {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<Agent[]>([]);
  const [searchedUser, setSearchedUser] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [newTarget, setNewTarget] = useState<string>("");
  const [showHistory, setShowHistory] = useState(false);
  const [isLoadingAccount, setIsLoadingAccount] = useState(false);
  const [accountDetails, setAccountDetails] = useState<any>(null);
  const [showAccount, setShowAccount] = useState(false);
  const [updateTarget, setUpdateTarget] = useState({
    all: false,
    single: false,
  });
  const [newEarnings, setNewEarnings] = useState<string>("");
  const [updateEarnings, setUpdateEarnings] = useState({
    all: false,
    single: false,
  });
  const [clickedAgent, setClickedAgent] = useState({
    id: "",
    firstname: "",
  });
  const { token } = useAppSelector((store) => store.auth);
  const itemsPerPage = 10;

  const fetchAgents = async () => {
    setLoading(true);
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/agents`,
        {
          headers: {
            Authorization: token,
          },
        }
      );

      setAgents(response.data.agents);
    } catch (error) {
      console.error("Failed to fetch agents:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAgents();
  }, []);

  const updateTargetHandler = async () => {
    if (!/^\d+$/.test(newTarget)) return;
    const endpoint = updateTarget.single
      ? `${clickedAgent.id}/update-target`
      : `update-target-all`;
    setLoading(true);
    try {
      const response = await axios.put(
        `${process.env.REACT_APP_API_URL}/agents/${endpoint}`,
        { target: Number(newTarget) },
        {
          headers: {
            Authorization: token,
          },
        }
      );

      fetchAgents();
      setSearchedUser("");
      setSearchResults([]);
      setNewTarget("");
      setUpdateTarget({ all: false, single: false });
      toast.success(response.data.message || "Target updated successfully!");
    } catch (error) {
      console.error("Failed to update agents target:", error);
      toast.error("Failed to update agents' targets.");
    } finally {
      setLoading(false);
    }
  };

  const updateEarningsHandler = async () => {
    if (!/^\d+$/.test(newEarnings)) return;
    const endpoint = updateEarnings.single
      ? `${clickedAgent.id}/update-earnings`
      : `update-earnings-all`;
    setLoading(true);
    try {
      const response = await axios.put(
        `${process.env.REACT_APP_API_URL}/agents/${endpoint}`,
        { expectedEarnings: Number(newEarnings) },
        {
          headers: {
            Authorization: token,
          },
        }
      );

      fetchAgents();
      setSearchedUser("");
      setSearchResults([]);
      setNewEarnings("");
      setUpdateEarnings({ all: false, single: false });
      toast.success(response.data.message || "Earnings updated successfully!");
    } catch (error) {
      console.error("Failed to update agents earnings:", error);
      toast.error("Failed to update agents' earnings.");
    } finally {
      setLoading(false);
    }
  };

  const getAccountDetails = async (agentId: string) => {
    setIsLoadingAccount(true);
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/agents/${agentId}/account-info`,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      setAccountDetails(response.data);
    } catch (error) {
      console.error("Failed to fetch account details:", error);
    } finally {
      setIsLoadingAccount(false);
    }
  };

  const searchForUsers = (value: string) => {
    setSearchedUser(value);
    if (value.length > 0) {
      const isPhoneNumber = /^\d+$/.test(value);
      const searchResult =
        agents?.filter((user: any) => {
          if (isPhoneNumber) {
            return user.phoneNumber
              ?.toLowerCase()
              .includes(value.toLowerCase());
          } else {
            return user.email?.toLowerCase().includes(value.toLowerCase());
          }
        }) || null;
      setSearchResults(searchResult);
    } else {
      setSearchResults([]);
    }
  };

  const indexOfLastPost = currentPage * itemsPerPage;
  const indexOfFirstPost = indexOfLastPost - itemsPerPage;
  const currentItems =
    searchResults.length > 0
      ? searchResults.slice(indexOfFirstPost, indexOfLastPost)
      : agents.slice(indexOfFirstPost, indexOfLastPost);

  const handlePagination = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const prevPage = () => {
    if (currentPage !== 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const nextPage = () => {
    if (currentPage !== Math.ceil(agents.length / itemsPerPage)) {
      setCurrentPage(currentPage + 1);
    }
  };

  return (
    <section>
      <section className="mb-6">
        <h1 className="text-2xl font-bold mb-4">Agents Management</h1>

        <button
          className="bg-secondary text-white border border-secondary px-4 py-2 rounded hover:bg-white hover:text-secondary mr-3"
          onClick={() => {
            setUpdateTarget({ all: !updateTarget.all, single: false });
          }}
        >
          Update All Agents Target
        </button>
        <button
          className="bg-secondary text-white border border-secondary px-4 py-2 rounded hover:bg-white hover:text-secondary"
          onClick={() => {
            setUpdateEarnings({ all: !updateEarnings.all, single: false });
          }}
        >
          Edit All Agents Earnings
        </button>
      </section>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold mb-3">Agents List</h2>
        <input
          type="text"
          placeholder="Search agents..."
          className="p-2 border rounded w-full sm:w-1/3 focus:outline-none focus:ring-2 focus:ring-secondary text-sm"
          value={searchedUser}
          onChange={(e) => {
            searchForUsers(e.target.value);
          }}
        />
      </div>
      <section className="overflow-x-auto w-full">
        <table className="table-auto w-full border text-nowrap text-sm">
          <thead>
            <tr className="bg-gray-100">
              <th className="px-4 py-2 border">SN</th>
              <th className="px-4 py-2 border">Full Name</th>
              <th className="px-4 py-2 border">Email</th>
              <th className="px-4 py-2 border">Category</th>
              <th className="px-4 py-2 border">Referral Code</th>
              <th className="px-4 py-2 border">Target</th>
              <th className="px-4 py-2 border">Update</th>
              <th className="px-4 py-2 border">Expected Earning</th>
              <th className="px-4 py-2 border">Edit</th>
              <th className="px-4 py-2 border">History</th>
              <th className="px-4 py-2 border">Account</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={10} className="text-center py-4">
                  <LoadingSpinner />
                </td>
              </tr>
            ) : agents.length > 0 ? (
              currentItems.map((agent, index) => (
                <tr key={agent._id}>
                  <td className="px-4 py-2 border">{index + 1}</td>
                  <td className="px-4 py-2 border capitalize">{`${agent?.firstname} ${agent?.lastname}`}</td>
                  <td className="px-4 py-2 border">{agent?.email}</td>
                  <td className="px-4 py-2 border">{agent?.category}</td>
                  <td className="px-4 py-2 border">{agent?.referralCode}</td>
                  <td className="px-4 py-2 border">{agent?.target || "--"}</td>
                  <td className="px-4 py-2 border">
                    <button
                      type="button"
                      className="p-2 text-sm border border-secondary text-secondary"
                      onClick={() => {
                        setUpdateTarget({ all: false, single: true });
                        setNewTarget(agent?.target.toString());
                        setClickedAgent({
                          id: agent?._id,
                          firstname: agent?.firstname,
                        });
                      }}
                    >
                      Update Target
                    </button>
                  </td>
                  <td className="px-4 py-2 border">
                    {agent?.expectedEarnings || "--"}
                  </td>
                  <td className="px-4 py-2 border">
                    <button
                      type="button"
                      className="p-2 text-sm border border-secondary text-secondary"
                      onClick={() => {
                        setUpdateEarnings({ all: false, single: true });
                        setNewEarnings(
                          agent?.expectedEarnings
                            ? agent?.expectedEarnings.toString()
                            : ""
                        );
                        setClickedAgent({
                          id: agent?._id,
                          firstname: agent?.firstname,
                        });
                      }}
                    >
                      Edit Earnings
                    </button>
                  </td>
                  <td className="px-4 py-2 border">
                    <button
                      type="button"
                      className="p-2 text-sm border border-secondary text-secondary"
                      onClick={() => {
                        setShowHistory(true);
                        setClickedAgent({
                          id: agent._id,
                          firstname: agent.firstname,
                        });
                      }}
                    >
                      View
                    </button>
                  </td>
                  <td className="px-4 py-2 border">
                    <button
                      type="button"
                      className="p-2 text-sm border border-secondary text-secondary"
                      onClick={() => {
                        setShowAccount(true);
                        setClickedAgent({
                          id: agent._id,
                          firstname: agent.firstname,
                        });
                        getAccountDetails(agent._id);
                      }}
                    >
                      View
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={10} className="text-center py-4">
                  No agents found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </section>
      <section className="p-3 my-5">
        <Pagination
          length={
            searchResults.length > 0 ? searchResults.length : agents.length
          }
          itemsPerPage={itemsPerPage}
          handlePagination={handlePagination}
          currentPage={currentPage}
          prevPage={prevPage}
          nextPage={nextPage}
        />
      </section>
      {(updateTarget.all || updateTarget.single) && (
        <UpdateTarget
          updateTarget={updateTarget}
          setUpdateTarget={setUpdateTarget}
          newTarget={newTarget}
          setNewTarget={setNewTarget}
          updateTargetHandler={updateTargetHandler}
          loading={loading}
          clickedAgent={clickedAgent}
        />
      )}
      {(updateEarnings.all || updateEarnings.single) && (
        <UpdateEarnings
          updateEarnings={updateEarnings}
          setUpdateEarnings={setUpdateEarnings}
          newEarnings={newEarnings}
          setNewEarnings={setNewEarnings}
          updateEarningsHandler={updateEarningsHandler}
          loading={loading}
          clickedAgent={clickedAgent}
        />
      )}
      {showHistory && (
        <ShowHistory
          setShowHistory={setShowHistory}
          clickedAgent={clickedAgent}
        />
      )}
      {showAccount && (
        <ShowAccount
          setShowAccount={setShowAccount}
          accountDetails={accountDetails}
          isLoadingAccount={isLoadingAccount}
          getAccountDetails={getAccountDetails}
          agentId={clickedAgent.id}
          setIsLoadingAccount={setIsLoadingAccount}
        />
      )}
    </section>
  );
};

export default Index;
