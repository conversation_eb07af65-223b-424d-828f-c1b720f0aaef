import { useEffect, useState } from "react";
import LoadingSpinner from "../../components/elements/LoadingSpinner";
import axios from "axios";
import toast from "react-hot-toast";
import { useAppSelector } from "../../redux/hooks";
import { BiChevronLeft } from "react-icons/bi";
import SelectABank from "../../components/SelectABank";

const ShowAccount = ({
  accountDetails,
  isLoadingAccount,
  setShowAccount,
  agentId,
  setIsLoadingAccount,
  getAccountDetails,
}: any) => {
  const [updateAccountDetails, setUpdateAccountDetails] = useState<any>(null);
  const { token } = useAppSelector((store) => store.auth);
  const [isVisible, setIsVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedBank, setSelectedBank] = useState<any>(null);
  const [verifiedAccountName, setVerifiedAccountName] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [inputValues, setInputValues] = useState({
    accountNumber: "",
    bankName: "",
  });

  useEffect(() => {
    const findBankAcountName = async () => {
      if (updateAccountDetails && !inputValues.accountNumber) {
        setError("Please enter account number and select a bank.");
        return;
      }
      if (updateAccountDetails && inputValues.accountNumber.length > 10) {
        setError("Account number must be at most 10 digits.");
        return;
      }
      const payload = {
        account_number: inputValues.accountNumber,
        bank_code: selectedBank?.bank_code,
      };
      setIsLoading(true);
      try {
        const response = await axios.post(
          `${process.env.REACT_APP_API_URL}/resolveAccount`,
          payload
        );
        if (response.data && response.data.data) {
          setVerifiedAccountName(response.data.data.account_name);
        } else {
          toast.error(
            response.data.message || "Failed to resolve bank account name."
          );
        }
      } catch (error) {
        console.error("Error resolving bank account name:", error);
      } finally {
        setIsLoading(false);
      }
    };

    findBankAcountName();
  }, [selectedBank]);

  const toggleModal = () => {
    setIsVisible(!isVisible);
  };

  const updateAccountDetailsHandler = async () => {
    setIsLoadingAccount(true);
    try {
      const res = await axios.put(
        `${process.env.REACT_APP_API_URL}/agents/${agentId}/update-account-info`,
        {
          accountNumber: inputValues.accountNumber,
          bankName: selectedBank?.bank_name,
        },
        {
          headers: {
            Authorization: token,
          },
        }
      );
      toast.success(res.data.message || "Account details updated successfully");
      getAccountDetails(agentId);
      setUpdateAccountDetails(null);
    } catch (error) {
      console.error("Error updating account details:", error);
    } finally {
      setIsLoadingAccount(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
      <div
        className="relative bg-white rounded-xl shadow-2xl w-full max-w-md p-8 animate-fadeIn"
        onClick={(e) => e.stopPropagation()}
      >
        <button
          className="absolute top-3 right-3 text-gray-400 hover:text-gray-700 text-xl"
          onClick={() => setShowAccount(false)}
          aria-label="Close"
        >
          &times;
        </button>
        {!updateAccountDetails && (
          <div className="mb-6">
            <h2 className="text-xl font-bold mb-2 text-secondary">
              Agent Account Details
            </h2>
            <p className="text-gray-500 text-sm mb-4">
              View the account details for this agent.
            </p>
          </div>
        )}
        {isLoadingAccount ? (
          <div className="flex justify-center py-8">
            <LoadingSpinner />
          </div>
        ) : accountDetails && !updateAccountDetails ? (
          <>
            <ul className="space-y-3">
              <li className="flex justify-between border-b pb-2">
                <span className="font-medium text-gray-700">Agent</span>
                <span className="text-secondary capitalize">
                  {accountDetails?.name || "--"}
                </span>
              </li>
              <li className="flex justify-between border-b pb-2">
                <span className="font-medium text-gray-700">
                  Account Number
                </span>
                <span className="text-secondary">
                  {accountDetails?.accountInfo?.accountNumber}
                </span>
              </li>
              <li className="flex justify-between border-b pb-2">
                <span className="font-medium text-gray-700">Bank name</span>
                <span className="text-secondary">
                  {accountDetails?.accountInfo?.bankName}
                </span>
              </li>
            </ul>
            <button
              type="button"
              className="text-secondary border border-secondary p-2 text-sm hover:bg-secondary hover:text-white transition-colors mt-4 w-full"
              onClick={() => setUpdateAccountDetails(true)}
            >
              Update Agent Account
            </button>
          </>
        ) : updateAccountDetails ? (
          // Update form
          <div className="space-y-6">
            <div className="text-center pb-4 border-b border-gray-200">
              <h3 className="text-xl font-bold text-secondary">
                Update Account Details
              </h3>
              <p className="text-gray-500 text-sm mt-1">
                Modify the agent's banking information
              </p>
            </div>
            <p>
              {error && <span className="text-red-500 text-xs">{error}</span>}
            </p>
            <div className="space-y-4">
              <div className="group">
                <label
                  htmlFor="accountNumber"
                  className="block text-sm font-semibold text-gray-700 mb-2"
                >
                  Account Number
                </label>
                <input
                  type="text"
                  id="accountNumber"
                  value={inputValues.accountNumber}
                  onChange={(e) => {
                    setInputValues({
                      ...inputValues,
                      accountNumber: e.target.value,
                    });
                    setVerifiedAccountName("");
                    setSelectedBank(null);
                  }}
                  className="w-full p-3 text-sm border-2 border-gray-200 rounded-lg shadow-sm focus:border-secondary focus:ring-2 focus:ring-secondary/20 transition-all duration-200 placeholder-gray-400"
                  placeholder="Enter account Number (e.g., **********)"
                />
              </div>

              <div className="group">
                <label
                  htmlFor="bankName"
                  className="block text-sm font-semibold text-gray-700 mb-2"
                >
                  Bank Name
                </label>
                <button
                  type="button"
                  onClick={toggleModal}
                  className="w-full p-3 text-sm border-2 border-gray-200 rounded-lg shadow-sm focus:border-secondary focus:ring-2 focus:ring-secondary/20 transition-all duration-200 placeholder-gray-400"
                  disabled={
                    isLoading ||
                    !inputValues.accountNumber.trim() ||
                    inputValues.accountNumber.length < 10
                  }
                >
                  {selectedBank ? selectedBank.bank_name : "Select Bank"}
                </button>
              </div>
              {isLoading && (
                <div className="flex justify-center py-4">
                  <LoadingSpinner />
                </div>
              )}
              {verifiedAccountName && (
                <div className="text-sm text-gray-600">
                  <label htmlFor="verifiedAccountName">
                    Verified Account Name:
                  </label>
                  <input
                    type="text"
                    name="verifiedAccountName"
                    id="verifiedAccountName"
                    value={verifiedAccountName}
                    readOnly
                    className="w-full p-2 border border-gray-300 rounded-md"
                  />
                </div>
              )}
            </div>

            <div className="flex gap-3 pt-4">
              <button
                type="button"
                onClick={() => {
                  setUpdateAccountDetails(null);
                  setInputValues({ accountNumber: "", bankName: "" });
                  setSelectedBank(null);
                  setVerifiedAccountName("");
                }}
                className="flex-1 flex items-center justify-center gap-2 p-3 text-sm font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded-lg transition-colors duration-200"
              >
                <BiChevronLeft className="w-4 h-4" />
                Cancel
              </button>
              <button
                onClick={updateAccountDetailsHandler}
                disabled={
                  isLoadingAccount ||
                  !inputValues.accountNumber.trim() ||
                  inputValues.accountNumber.length < 10 ||
                  !selectedBank?.bank_name ||
                  !verifiedAccountName
                }
                className="flex-1 p-3 text-sm font-medium text-white bg-secondary hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed rounded-lg transition-colors duration-200 shadow-md hover:shadow-lg"
              >
                {isLoadingAccount ? (
                  <span className="flex items-center justify-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Saving...
                  </span>
                ) : (
                  "Save Changes"
                )}
              </button>
            </div>
          </div>
        ) : (
          <p className="text-gray-500 text-center py-8">
            No history found for this agent.
          </p>
        )}
        <div className="flex justify-end mt-8">
          <button
            onClick={() => setShowAccount(false)}
            className="bg-gray-200 border border-gray-300 text-gray-700 px-5 py-2 rounded-lg hover:bg-gray-300 transition text-sm"
          >
            Close
          </button>
        </div>
      </div>
      {isVisible && (
        <SelectABank
          toggleModal={toggleModal}
          bank={inputValues.bankName}
          setBank={setSelectedBank}
        />
      )}
    </div>
  );
};

export default ShowAccount;
