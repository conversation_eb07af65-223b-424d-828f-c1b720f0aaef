import { useState } from "react";
import LoadingSpinner from "../../components/elements/LoadingSpinner";
import axios from "axios";
import { useAppSelector } from "../../redux/hooks";

const ShowHistory = ({ setShowHistory, clickedAgent }: any) => {
  const [month, setMonth] = useState("");
  const [year, setYear] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [agentHistory, setAgentHistory] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const { token } = useAppSelector((store) => store.auth);

  const getAgentHistory = async () => {
    if (!month || !year) {
      setError("Please select both month and year.");
      return;
    }
    setError(null);
    setIsLoading(true);
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/agents/${clickedAgent.id}/history?month=${month}&year=${year}`,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      setAgentHistory(response.data.history);
    } catch (error) {
      console.error("Error fetching agent history:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
      <div
        className="relative bg-white rounded-xl shadow-2xl w-full max-w-md p-8 animate-fadeIn"
        onClick={(e) => e.stopPropagation()}
      >
        <button
          className="absolute top-3 right-3 text-gray-400 hover:text-gray-700 text-xl"
          onClick={() => setShowHistory(false)}
          aria-label="Close"
        >
          &times;
        </button>
        <div className="mb-6">
          <h2 className="text-2xl font-bold mb-1 text-primary">
            Agent History
          </h2>
          <p className="text-gray-600 text-sm mb-3">
            Reviewing history for{" "}
            <span className="capitalize font-semibold text-secondary">
              {clickedAgent.firstname}
            </span>
            .
          </p>
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-600 rounded px-3 py-1 mb-2 text-xs">
              {error}
            </div>
          )}
        </div>
        <div className="flex flex-col sm:flex-row items-center gap-2 mb-6">
          <select
            name="month"
            value={month}
            onChange={(e) => setMonth(e.target.value)}
            id="month"
            className="p-2 border rounded text-sm focus:outline-none focus:ring-2 focus:ring-secondary"
          >
            <option value="">Month</option>
            <option value="1">Jan</option>
            <option value="2">Feb</option>
            <option value="3">Mar</option>
            <option value="4">Apr</option>
            <option value="5">May</option>
            <option value="6">Jun</option>
            <option value="7">Jul</option>
            <option value="8">Aug</option>
            <option value="9">Sep</option>
            <option value="10">Oct</option>
            <option value="11">Nov</option>
            <option value="12">Dec</option>
          </select>
          <select
            name="year"
            value={year}
            onChange={(e) => setYear(e.target.value)}
            id="year"
            className="p-2 border rounded text-sm focus:outline-none focus:ring-2 focus:ring-secondary"
          >
            <option value="">Year</option>
            {Array.from({ length: 5 }, (_, i) => {
              const year = new Date().getFullYear() - i;
              return (
                <option key={year} value={year}>
                  {year}
                </option>
              );
            })}
          </select>
          <button
            type="button"
            className="bg-secondary text-white px-4 py-2 rounded shadow hover:bg-secondary-dark transition disabled:opacity-50"
            onClick={getAgentHistory}
            disabled={isLoading || !month || !year}
          >
            {isLoading ? "Searching..." : "Search"}
          </button>
        </div>
        <div className="bg-gray-50 rounded-lg p-5 shadow-inner min-h-[120px]">
          {isLoading ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner />
            </div>
          ) : agentHistory ? (
            <div>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <div className="text-xs text-gray-500">Agent</div>
                  <div className="font-semibold text-secondary capitalize">
                    {agentHistory?.name || "--"}
                  </div>
                </div>
                <div>
                  <div className="text-xs text-gray-500">Total Referrals</div>
                  <div className="font-semibold text-primary">
                    {agentHistory?.totalReferrals}
                  </div>
                </div>
                <div>
                  <div className="text-xs text-gray-500">Total Sales</div>
                  <div className="font-semibold text-primary">
                    {agentHistory?.totalSales}
                  </div>
                </div>
                <div>
                  <div className="text-xs text-gray-500">Referral Earnings</div>
                  <div className="font-semibold text-green-600">
                    {agentHistory?.totalReferralEarnings}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <p className="text-gray-400 text-center py-8">
              No history found for this agent.
            </p>
          )}
        </div>
        <div className="flex justify-end mt-8">
          <button
            onClick={() => setShowHistory(false)}
            className="bg-white border border-gray-300 text-gray-700 px-5 py-2 rounded-lg hover:bg-gray-100 transition"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ShowHistory;
