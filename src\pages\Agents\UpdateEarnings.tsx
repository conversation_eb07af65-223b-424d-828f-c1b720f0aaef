const UpdateEarnings = ({
  setUpdateEarnings,
  setNewEarnings,
  loading,
  updateEarnings,
  clickedAgent,
  newEarnings,
  updateEarningsHandler,
}: any) => {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
      <div
        className="relative bg-white rounded-xl shadow-2xl w-full max-w-md p-8 animate-fadeIn"
        onClick={(e) => e.stopPropagation()}
      >
        <button
          className="absolute top-3 right-3 text-gray-400 hover:text-gray-700 text-xl"
          onClick={() => {
            setUpdateEarnings({ all: false, single: false });
            setNewEarnings("");
          }}
          aria-label="Close"
          disabled={loading}
        >
          &times;
        </button>
        <div className="mb-6">
          <h2 className="text-xl font-bold mb-2 text-secondary">
            {updateEarnings.all
              ? "Update Earnings for All Agents"
              : `Update Earnings for ${clickedAgent.firstname}`}
          </h2>
          <p className="text-gray-500 text-sm">
            {updateEarnings.all
              ? "Set new expected earnings for all agents."
              : "Set new expected earnings for this agent."}
          </p>
        </div>
        <div className="mb-4">
          <label className="block text-gray-700 mb-1 font-medium">
            New Earnings
          </label>
          <input
            type="number"
            min={0}
            value={newEarnings}
            onChange={(e) => setNewEarnings(e.target.value)}
            className="border border-gray-300 p-2 rounded w-full focus:outline-none focus:ring-2 focus:ring-secondary"
            placeholder="Enter new earnings"
            disabled={loading}
          />
        </div>
        <div className="flex justify-end gap-2 mt-6">
          <button
            onClick={updateEarningsHandler}
            className="bg-secondary border border-secondary text-white px-5 py-2 rounded-lg hover:bg-white hover:text-secondary transition"
            disabled={loading || !newEarnings}
          >
            {loading ? "Updating..." : "Update"}
          </button>
          <button
            onClick={() => {
              setUpdateEarnings({ all: false, single: false });
              setNewEarnings("");
            }}
            className="bg-gray-200 border border-gray-300 text-gray-700 px-5 py-2 rounded-lg hover:bg-gray-300 transition"
            disabled={loading}
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

export default UpdateEarnings;
