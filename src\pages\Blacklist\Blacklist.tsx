import { useState, useEffect } from "react";
import { <PERSON>a<PERSON><PERSON>, FaTrash, FaPlus, FaSearch, FaHistory } from "react-icons/fa";
import { useAppDispatch, useAppSelector } from "../../redux/hooks";
import {
  getAllBlacklisted,
  addToBlacklist,
  removeFromBlacklist,
} from "../../redux/thunk";
import toast from "react-hot-toast";
import AddToBlacklistModal from "../../components/modals/AddToBlacklistModal";
import ViewBlacklistModal from "../../components/modals/ViewBlacklistModal";
import ViewTransactionsModal from "../../components/modals/ViewTransactionsModal";
import ConfirmDeleteModal from "../../components/modals/ConfirmDeleteModal";

const formatDate = (dateStr: string): string => {
  try {
    const date = new Date(dateStr);
    const options: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "long",
      day: "numeric",
    };
    return date.toLocaleDateString("en-US", options);
  } catch (e) {
    return "Invalid Date";
  }
};

const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat("en-NG", {
    style: "currency",
    currency: "NGN",
    minimumFractionDigits: 2,
  })
    .format(amount)
    .replace("NGN", "₦");
};

const Blacklist = () => {
  const dispatch = useAppDispatch();
  const { blacklist, status, error } = useAppSelector(
    (state) => state.blacklist
  );

  const [searchTerm, setSearchTerm] = useState("");
  const [showAddModal, setShowAddModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showTransactionsModal, setShowTransactionsModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Filter blacklist based on search term
  const filteredBlacklist = blacklist.filter((item: any) => {
    const customer = item.customerId;
    const searchLower = searchTerm.toLowerCase();
    return (
      customer.email.toLowerCase().includes(searchLower) ||
      customer.firstName.toLowerCase().includes(searchLower) ||
      customer.lastName.toLowerCase().includes(searchLower) ||
      customer.phoneNumber.includes(searchTerm)
    );
  });

  // Pagination calculations
  const totalPages = Math.ceil(filteredBlacklist.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems = filteredBlacklist.slice(startIndex, endIndex);

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePrevious = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  useEffect(() => {
    const fetchBlacklist = async () => {
      setIsLoading(true);
      try {
        await dispatch(getAllBlacklisted()).unwrap();
      } catch (error: any) {
        toast.error(error || "Failed to fetch blacklist");
      } finally {
        setIsLoading(false);
      }
    };

    fetchBlacklist();
  }, [dispatch]);

  const handleAddToBlacklist = async (email: string) => {
    try {
      await dispatch(addToBlacklist({ email })).unwrap();
      toast.success("User added to blacklist successfully");
      setShowAddModal(false);
      // Refresh the blacklist
      dispatch(getAllBlacklisted());
    } catch (error: any) {
      toast.error(error || "Failed to add user to blacklist");
    }
  };

  const handleRemoveFromBlacklist = async (email: string) => {
    try {
      await dispatch(removeFromBlacklist({ email })).unwrap();
      toast.success("User removed from blacklist successfully");
      setShowDeleteModal(false);
      setSelectedUser(null);
      // Refresh the blacklist
      dispatch(getAllBlacklisted());
    } catch (error: any) {
      toast.error(error || "Failed to remove user from blacklist");
    }
  };

  const handleViewUser = (user: any) => {
    setSelectedUser(user);
    setShowViewModal(true);
  };

  const handleViewTransactions = (user: any) => {
    setSelectedUser(user);
    setShowTransactionsModal(true);
  };

  const handleDeleteUser = (user: any) => {
    setSelectedUser(user);
    setShowDeleteModal(true);
  };

  const handleCloseModals = () => {
    setShowAddModal(false);
    setShowViewModal(false);
    setShowTransactionsModal(false);
    setShowDeleteModal(false);
    setSelectedUser(null);
  };

  if (isLoading || status === "loading") {
    return (
      <main className="overflow-x-auto w-full">
        <div className="bg-white rounded-md shadow-md pb-6">
          <div className="p-6 flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading blacklist...</p>
            </div>
          </div>
        </div>
      </main>
    );
  }

  if (error) {
    return (
      <main className="overflow-x-auto w-full">
        <div className="bg-white rounded-md shadow-md pb-6">
          <div className="p-6 flex items-center justify-center">
            <div className="text-center">
              <p className="text-red-600 mb-4">
                Error loading blacklist: {error}
              </p>
              <button
                onClick={() => dispatch(getAllBlacklisted())}
                className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="overflow-x-auto w-full">
      <div className="bg-white rounded-md shadow-md pb-6">
        {/* Header */}
        <div className="p-6 border-b">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-semibold text-gray-800">
              Blacklisted Users
            </h1>
            <button
              onClick={() => setShowAddModal(true)}
              className="flex items-center gap-2 bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors"
            >
              <FaPlus className="w-4 h-4" />
              Add to Blacklist
            </button>
          </div>
        </div>

        {/* Search and Stats */}
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-2">
              <h2 className="text-lg font-medium text-gray-800">
                Blacklist Management
              </h2>
              <span className="text-gray-500">→ Overview</span>
            </div>

            {/* Search Input */}
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaSearch className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search by name, email, or phone..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-red-500 focus:border-red-500 text-sm w-80"
              />
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-red-50 p-4 rounded-lg">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 rounded-lg">
                  <FaTrash className="w-5 h-5 text-red-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-600">
                    Total Blacklisted
                  </p>
                  <p className="text-2xl font-bold text-red-600">
                    {blacklist.length}
                  </p>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center">
                <div className="p-2 bg-gray-100 rounded-lg">
                  <FaSearch className="w-5 h-5 text-gray-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-600">
                    Search Results
                  </p>
                  <p className="text-2xl font-bold text-gray-600">
                    {filteredBlacklist.length}
                  </p>
                </div>
              </div>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FaEye className="w-5 h-5 text-blue-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-gray-600">
                    Current Page
                  </p>
                  <p className="text-2xl font-bold text-blue-600">
                    {currentPage} of {totalPages}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {filteredBlacklist.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">
                {searchTerm
                  ? `No blacklisted users found for "${searchTerm}"`
                  : "No users in blacklist"}
              </p>
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm("")}
                  className="text-red-600 hover:text-red-700 text-sm underline mt-2"
                >
                  Clear search
                </button>
              )}
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="w-full" style={{ minWidth: "900px" }}>
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="p-3 text-left text-sm font-medium text-gray-700">
                        S/N
                      </th>
                      <th className="p-3 text-left text-sm font-medium text-gray-700">
                        Name
                      </th>
                      <th className="p-3 text-left text-sm font-medium text-gray-700">
                        Email
                      </th>
                      <th className="p-3 text-left text-sm font-medium text-gray-700">
                        Phone
                      </th>
                      <th className="p-3 text-left text-sm font-medium text-gray-700">
                        Wallet Balance
                      </th>
                      <th className="p-3 text-left text-sm font-medium text-gray-700">
                        Blacklisted Date
                      </th>
                      <th className="p-3 text-left text-sm font-medium text-gray-700">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {currentItems.map((item: any, index: number) => {
                      const customer = item.customerId;
                      return (
                        <tr key={item._id} className="hover:bg-gray-50">
                          <td className="p-3 text-sm text-gray-900">
                            {String(startIndex + index + 1).padStart(3, "0")}
                          </td>
                          <td className="p-3 text-sm text-gray-900">
                            <div className="font-medium">
                              {customer.firstName} {customer.lastName}
                            </div>
                            <div className="text-xs text-gray-500">
                              ID: {customer._id}
                            </div>
                          </td>
                          <td className="p-3 text-sm text-gray-900">
                            {customer.email}
                          </td>
                          <td className="p-3 text-sm text-gray-900">
                            {customer.phoneNumber}
                          </td>
                          <td className="p-3 text-sm text-gray-900">
                            <span className="font-medium text-green-600">
                              {formatCurrency(
                                customer.virtualAccount.walletbalance
                              )}
                            </span>
                          </td>
                          <td className="p-3 text-sm text-gray-900">
                            {formatDate(item.createdAt)}
                          </td>
                          <td className="p-3">
                            <div className="flex items-center gap-2">
                              <button
                                onClick={() => handleViewUser(item)}
                                className="flex items-center gap-1 text-blue-600 hover:text-blue-700 font-medium text-sm px-2 py-1 rounded"
                                title="View Details"
                              >
                                <FaEye className="w-3 h-3" />
                                View
                              </button>
                              <button
                                onClick={() => handleViewTransactions(item)}
                                className="flex items-center gap-1 text-green-600 hover:text-green-700 font-medium text-sm px-2 py-1 rounded"
                                title="View Transactions"
                              >
                                <FaHistory className="w-3 h-3" />
                                Transactions
                              </button>
                              <button
                                onClick={() => handleDeleteUser(item)}
                                className="flex items-center gap-1 text-red-600 hover:text-red-700 font-medium text-sm px-2 py-1 rounded"
                                title="Remove from Blacklist"
                              >
                                <FaTrash className="w-3 h-3" />
                                Remove
                              </button>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-6 px-4">
                  <div className="text-sm text-gray-700">
                    Showing {startIndex + 1} to{" "}
                    {Math.min(endIndex, filteredBlacklist.length)} of{" "}
                    {filteredBlacklist.length} results
                    {searchTerm && (
                      <span className="text-red-600 ml-1">
                        (filtered by "{searchTerm}")
                      </span>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={handlePrevious}
                      disabled={currentPage === 1}
                      className="px-3 py-1 text-sm border rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>

                    {/* Page numbers */}
                    <div className="flex space-x-1">
                      {Array.from(
                        { length: Math.min(5, totalPages) },
                        (_, i) => {
                          let pageNum: number;
                          if (totalPages <= 5) {
                            pageNum = i + 1;
                          } else if (currentPage <= 3) {
                            pageNum = i + 1;
                          } else if (currentPage >= totalPages - 2) {
                            pageNum = totalPages - 4 + i;
                          } else {
                            pageNum = currentPage - 2 + i;
                          }

                          return (
                            <button
                              key={pageNum}
                              onClick={() => handlePageChange(pageNum)}
                              className={`px-3 py-1 text-sm border rounded-md ${
                                currentPage === pageNum
                                  ? "bg-red-600 text-white border-red-600"
                                  : "hover:bg-gray-50"
                              }`}
                            >
                              {pageNum}
                            </button>
                          );
                        }
                      )}
                    </div>

                    <button
                      onClick={handleNext}
                      disabled={currentPage === totalPages}
                      className="px-3 py-1 text-sm border rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Modals */}
      {showAddModal && (
        <AddToBlacklistModal
          isOpen={showAddModal}
          onClose={handleCloseModals}
          onAdd={handleAddToBlacklist}
        />
      )}

      {showViewModal && selectedUser && (
        <ViewBlacklistModal
          isOpen={showViewModal}
          onClose={handleCloseModals}
          userData={selectedUser}
        />
      )}

      {showTransactionsModal && selectedUser && (
        <ViewTransactionsModal
          isOpen={showTransactionsModal}
          onClose={handleCloseModals}
          userData={selectedUser}
        />
      )}

      {showDeleteModal && selectedUser && (
        <ConfirmDeleteModal
          isOpen={showDeleteModal}
          onClose={handleCloseModals}
          onConfirm={() =>
            handleRemoveFromBlacklist(selectedUser.customerId.email)
          }
          title="Remove from Blacklist"
          message={`Are you sure you want to remove ${selectedUser.customerId.firstName} ${selectedUser.customerId.lastName} from the blacklist?`}
          confirmText="Remove"
          confirmButtonClass="bg-red-600 hover:bg-red-700"
        />
      )}
    </main>
  );
};

export default Blacklist;
