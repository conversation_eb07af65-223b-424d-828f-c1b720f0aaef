import { useState, useEffect } from "react";
import { useAppSelector } from "../../redux/hooks";
import { RootState } from "../../redux/store";
import { FaSortDown } from "react-icons/fa6";
import BarChart from "./BarChart";
import ActivityLog from "../../components/dashboard/ActivityLog";
import FirstRowMetrics from "./FirstRowMetrics";
import TotalUsersLineGraph from "./TotalUsersLineGraph";
import Expectation from "./Expectation";
import OrderDemo from "./OrderDemo";
import {
  getCustomerExpectationReached,
  getOrderDemographics,
  getTotalUsers,
  getWalletLoanAnalytics,
} from "./data";
import { formatNumber } from "../../redux/thunk";

const AdminDashboard = () => {
  const { token } = useAppSelector((store: RootState) => store.auth);
  const [selectedLineGraphMonth, setSelectedLineGraphMonth] = useState<string>(
    new Date().toISOString().slice(0, 7)
  );
  const [selectedBarChartMonth, setSelectedBarChartMonth] = useState<string>(
    new Date().toISOString().slice(0, 7)
  );
  const [isLoading, setIsLoading] = useState(false);
  const [lineGraphData, setLineGraphData] = useState<any>([]);
  const [barChartData, setBarChartData] = useState<any>({});
  const [customerExData, setCustomerExData] = useState<any>({});
  const [walletLoanAnalytics, setWalletLoanAnalytics] = useState<any>({});

  const [loanPercent, setLoanPercent] = useState({
    approved: 0,
    unpaid: 0,
  });

  useEffect(() => {
    const approvedLoan = walletLoanAnalytics?.monthlyData?.approvedLoan || 0;
    const unpaidLoan = walletLoanAnalytics?.monthlyData?.totalUnpaidLoan || 0;
    const total = approvedLoan + unpaidLoan;

    const approvedPercent = Math.round((approvedLoan / total) * 100);
    const unpaidPercent = Math.round((unpaidLoan / total) * 100);

    setLoanPercent({
      approved: approvedPercent,
      unpaid: unpaidPercent,
    });
  }, [
    walletLoanAnalytics?.monthlyData?.approvedLoan,
    walletLoanAnalytics?.monthlyData?.totalUnpaidLoan,
  ]);

  const lineGraphYear = selectedLineGraphMonth.split("-")[0];
  const lineGraphMonth = selectedLineGraphMonth.split("-")[1];

  const barChartYear = selectedBarChartMonth.split("-")[0];
  const barChartMonth = selectedBarChartMonth.split("-")[1];

  useEffect(() => {
    setIsLoading(true);
    const dataPromise = getTotalUsers(
      lineGraphYear,
      lineGraphMonth,
      token ?? ""
    );
    dataPromise
      .then((res: any) => {
        setLineGraphData(res);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [lineGraphYear, lineGraphMonth, token]);

  useEffect(() => {
    setIsLoading(true);
    const dataPromise = getOrderDemographics(
      barChartYear,
      barChartMonth,
      token ?? ""
    );
    dataPromise
      .then((res: any) => {
        setBarChartData(res);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [barChartYear, barChartMonth, token]);

  useEffect(() => {
    setIsLoading(true);
    const dataPromise = getCustomerExpectationReached(token ?? "");
    dataPromise
      .then((res: any) => {
        setCustomerExData(res.expectationReached);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [token]);

  useEffect(() => {
    setIsLoading(true);
    const dataPromise = getWalletLoanAnalytics(token ?? "");
    dataPromise
      .then((res: any) => {
        setWalletLoanAnalytics(res);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [token]);

  return (
    <main>
      {/* First row*/}
      <FirstRowMetrics
        walletLoanAnalytics={walletLoanAnalytics}
        data={lineGraphData}
        isLoading={isLoading}
      />
      {/* Second row */}
      <TotalUsersLineGraph
        isLoading={isLoading}
        data={lineGraphData}
        selectedLineGraphMonth={selectedLineGraphMonth}
        setSelectedLineGraphMonth={setSelectedLineGraphMonth}
      />
      {/* Third row */}
      <section className="flex items-end gap-3">
        <section className="w-2/3 p-5 bg-white shadow-md rounded-md">
          <article className="flex justify-between">
            <h3 className="font-bold text-lg mb-3">Total Orders</h3>
            <section>
              <input
                type="month"
                name="month"
                id="month"
                className="border text-sm p-1"
                value={selectedBarChartMonth}
                onChange={(e) => setSelectedBarChartMonth(e.target.value)}
                disabled={isLoading}
              />
            </section>
          </article>
          <section className={isLoading ? "animate-pulse" : ""}>
            <BarChart barChartData={barChartData} />
          </section>
        </section>

        <section className={`${isLoading && "animate-pulse"} w-1/2`}>
          <OrderDemo data={barChartData} />
        </section>
      </section>

      {/* fourth row */}
      <section className="flex my-10 justify-between items-start">
        <section className="">
          <section className="font-mont mb-24">
            <div className="flex gap-3 items-center">
              <h3 className="font-medium text-2xl">Loan Requests</h3>
              <span className="flex gap-2 text-sm">
                This month <FaSortDown />
              </span>
            </div>
            <article className="my-5">
              <p className="font-medium text-xs">Approved loans</p>
              <p className="font-medium text-3xl">
                {formatNumber(walletLoanAnalytics?.monthlyData?.approvedLoan) ||
                  0}
              </p>
              <div className="border bg-gray-200 rounded-full w-full h-3">
                <div
                  className="bg-secondary h-full rounded-full"
                  style={{ width: `${loanPercent.approved}%` }}
                ></div>
              </div>
            </article>
            <article className="my-5">
              <p className="font-medium text-xs">Disapproved loans</p>
              <p className="font-medium text-3xl">
                {formatNumber(
                  walletLoanAnalytics?.monthlyData?.totalUnpaidLoan
                ) || 0}
              </p>
              <div className="border bg-gray-200 rounded-full w-full h-3">
                <div
                  className="bg-[#FF1C0E] h-full rounded-full"
                  style={{ width: `${loanPercent.unpaid}%` }}
                ></div>
              </div>
            </article>
          </section>
          <Expectation isLoading={isLoading} stats={customerExData} />
        </section>
        <ActivityLog />
      </section>
    </main>
  );
};

export default AdminDashboard;
