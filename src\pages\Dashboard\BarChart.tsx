import {
  Chart as ChartJs,
  Tooltip,
  Legend,
  BarElement,
  ArcElement,
  ChartOptions,
  LinearScale,
  CategoryScale,
} from "chart.js";
import { Bar } from "react-chartjs-2";

ChartJs.register(
  Tooltip,
  Legend,
  ArcElement,
  BarElement,
  LinearScale,
  CategoryScale
);

const BarChart = ({ barChartData }: any) => {
  const yValues = barChartData?.orderDemographics?.y || [0];
  const minY = Math.min(...yValues);
  const maxY = Math.max(...yValues);

  const barData = {
    labels: barChartData?.orderDemographics?.x || [
      ["One-Time", "Orders"],
      ["Two-Time", "Orders"],
      ["Three-Time", "Orders"],
      ["Four-Time", "Orders"],
      ["Five-Time", "Orders"],
      ["10+ Times", "Orders"],
    ],
    datasets: [
      {
        label: "Flexible Users",
        data: barChartData?.orderDemographics?.flexible || [
          12, 19, 20, 15, 7, 9,
        ],
        backgroundColor: "#FF9500",
        borderRadius: {
          topLeft: 100,
          topRight: 100,
          bottomLeft: 100,
          bottomRight: 100,
        },
        maxBarThickness: 15,
        borderSkipped: false,
        barPercentage: 0.7,
      },
      {
        label: "Outright Users",
        data: barChartData?.orderDemographics?.outright || [
          10, 15, 20, 4, 6, 8,
        ],
        backgroundColor: "#008B50",
        borderRadius: {
          topLeft: 100,
          topRight: 100,
          bottomLeft: 100,
          bottomRight: 100,
        },
        maxBarThickness: 15,
        borderSkipped: false,
        barPercentage: 0.7,
      },
    ],
  };

  const barChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: "top" as const,
        labels: {
          usePointStyle: true,
          pointStyle: "circle",
          boxWidth: 10,
          padding: 10,
        },
      },
      title: {
        display: false,
        text: "Order Frequency by User Type",
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        categoryPercentage: 0.7,
        ticks: {
          minRotation: 0,
          maxRotation: 0,
        },
      },
      y: {
        min: minY,
        max: maxY,
      },
    },
  };

  return (
    <section className="h-[280px]">
      <Bar options={barChartOptions} data={barData} />
    </section>
  );
};

export default BarChart;
