import { Doughnut } from "react-chartjs-2";
import { Chart as ChartJS, ArcElement, <PERSON><PERSON><PERSON>, Legend } from "chart.js";

// Register required components
ChartJS.register(ArcElement, Tooltip, Legend);

const DonutChart = ({ data }: any) => {
  return (
    <div style={{ width: "100px", height: "100px", margin: "0 auto" }}>
      <Doughnut
        data={data}
        options={{
          responsive: true,
          cutout: "70%",
          plugins: {
            legend: {
              position: "bottom",
              display: false,
            },
            title: {
              display: false,
              text: "Total Loans",
            },
          },
        }}
      />
    </div>
  );
};

export default DonutChart;
