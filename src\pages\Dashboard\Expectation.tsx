import { useState } from "react";
import { formatNumber } from "../../redux/thunk";

const Expectation = ({ stats, isLoading }: any) => {
  const [active, setActive] = useState("thisMonth");

  return (
    <section className={`${isLoading && "animate-pulse"} font-mont`}>
      <div className="flex justify-between items-center mb-10">
        <h3 className="font-semibold text-lg">Expectation Reached</h3>
      </div>
      <section className="bg-white shadow-md rounded-md p-10 mb-5 mx-auto text-center">
        <p className="text-xs text-gray-400">Verified Users Reached</p>
        <p className="text-5xl my-3">
          {Object.keys(stats).length > 0 ? formatNumber(stats[active]) : "0"}
        </p>
        <div className="flex justify-between">
          <button
            className={`text-xs font-semibold ${
              active === "today" ? "text-black" : "text-gray-400"
            }`}
            onClick={() => setActive("today")}
          >
            Day
          </button>
          <button
            className={`text-xs font-semibold ${
              active === "thisWeek" ? "text-black" : "text-gray-400"
            }`}
            onClick={() => setActive("thisWeek")}
          >
            Week
          </button>
          <button
            className={`text-xs font-semibold ${
              active === "thisMonth" ? "text-black" : "text-gray-400"
            }`}
            onClick={() => setActive("thisMonth")}
          >
            Month
          </button>
          <button
            className={`text-xs font-semibold ${
              active === "thisYear" ? "text-black" : "text-gray-400"
            }`}
            onClick={() => setActive("thisYear")}
          >
            Year
          </button>
        </div>
      </section>
    </section>
  );
};

export default Expectation;
