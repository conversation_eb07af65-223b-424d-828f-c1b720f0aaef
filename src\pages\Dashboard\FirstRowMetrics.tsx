import { FaSortDown } from "react-icons/fa6";
import Donut<PERSON>hart from "./DonutChart";
import { formatNumber } from "../../redux/thunk";
import { useState } from "react";
import Modal, { ModalContent } from "../../components/elements/Modal";
import UserBox from "./UserBox";
interface FirstRowMetricsProps {
  data: {
    totalUsers: {
      total: number;
      flexible: number;
      outright: number;
    };
    userType: UserData[];
    categoryType: {
      _id: string;
      count: number | string;
    }[];
    growthOverTime: {
      x: string[];
      y: number[];
      flexible: number[];
      outright: number[];
    };
  };
  isLoading: boolean;
  walletLoanAnalytics: {
    TotalLoan: number;
    TotalPaidLoan: number;
    TotalUnpaidLoan: number;
    TotalBadLoan: number;
    monthlyData: {
      approvedLoan: number;
      totalUnpaidLoan: number;
    };
  };
}

interface UserData {
  _id?: string;
  count?: number | string;
  navigateUrl?: string;
  total?: number;
  flexible?: number;
  outright?: number;
}

const FirstRowMetrics = ({
  walletLoanAnalytics,
  data,
  isLoading,
}: FirstRowMetricsProps) => {
  const [viewModal, setViewModal] = useState<string>("");
  const totalPaidLoan = Math.round(walletLoanAnalytics?.TotalPaidLoan) || 0;
  const totalUnpaidLoan = Math.round(walletLoanAnalytics?.TotalUnpaidLoan) || 0;
  const donutData = {
    labels: ["Total Paid Loans", "Total Unpaid Loans"],
    datasets: [
      {
        label: "Total loans",
        data: [totalUnpaidLoan, totalPaidLoan],
        backgroundColor: ["#FF1C0E", "#008B50BF"],
        hoverOffset: 4,
      },
    ],
  };

  return (
    <section className="flex items-start justify-between gap-3 mb-5">
      <section className="flex items-center gap-5 p-5 shadow-md font-mont rounded-xl bg-white w-fit">
        <div
          className={`${isLoading && "animate-pulse"} flex flex-col gap-3 px-5`}
        >
          <button type="button" onClick={() => setViewModal("totalUsers")}>
            <h2 className="inline-flex font-medium mb-3 text-xl items-center gap-2">
              Total Users <FaSortDown className="w-4 h-4" />
            </h2>
          </button>
          <p className="opacity-70">All Users</p>
          <p className="font-semibold text-2xl">
            {data?.totalUsers?.total || 0}
          </p>
        </div>
        <div
          className={`${
            isLoading && "animate-pulse"
          } flex flex-col gap-3 px-5 border-l`}
        >
          <button type="button" onClick={() => setViewModal("userCategory")}>
            <h2 className="inline-flex cursor-pointer font-medium mb-3 text-lg items-center gap-2">
              User Category <FaSortDown className="w-4 h-4" />
            </h2>
          </button>
          <img src="/assets/Bars.svg" alt="" className="w-20" />
          <p className="font-semibold text-2xl">
            {data?.categoryType?.length || 0}
          </p>
        </div>
        <div
          className={`${
            isLoading && "animate-pulse"
          } flex flex-col gap-3 px-5 border-l`}
        >
          <button type="button" onClick={() => setViewModal("userType")}>
            <h2 className="inline-flex cursor-pointer font-medium mb-3 text-lg items-center gap-2">
              User Type <FaSortDown className="w-4 h-4" />
            </h2>
          </button>
          <img src="/assets/Bars2.svg" alt="" className="w-20" />
          <p className="font-semibold text-2xl">
            {data?.userType?.length || 0}
          </p>
        </div>
      </section>
      <section
        className={`${
          isLoading && "animate-pulse"
        } bg-white shadow-md rounded-xl p-5 w-[32%]`}
      >
        <h2 className="text-xs mb-3 text-center">
          Total Loans -
          <span className="text-lg font-medium">
            {" "}
            ₦{formatNumber(Math.round(walletLoanAnalytics?.TotalLoan))}
          </span>
        </h2>
        <DonutChart data={donutData} />
        <section className="flex justify-between items-center mt-5">
          <article className="text-xs text-center">
            <div className="flex items-center font-semibold gap-2">
              <div className="w-3 h-3 rounded-full bg-[#008B50BF]"></div>
              <p>₦{formatNumber(totalPaidLoan)}</p>
            </div>
            <p className="text-xs text-gray-500">Total Paid</p>
          </article>
          <article className="text-xs text-center">
            <div className="flex items-center font-semibold gap-2">
              <div className="w-3 h-3 rounded-full bg-[#FF1C0E]"></div>
              <p>₦{formatNumber(totalUnpaidLoan)}</p>
            </div>
            <p className="text-xs text-gray-500">Total Unpaid</p>
          </article>
        </section>
        {viewModal && (
          <Modal
            open={true}
            onClose={() => setViewModal("")}
            className="flex items-center justify-center"
          >
            <ModalContent className="mx-3 p-3 rounded-md shadow-lg flex flex-col justify-center items-center bg-white w-[80%] md:w-[55%] lg:w-[50%] xl:w-[32%] ">
              <section className="w-full p-2">
                <h2 className="font-semibold text-2xl font-nunito">
                  {viewModal === "totalUsers"
                    ? "Total Users"
                    : viewModal === "userCategory"
                    ? "User Category"
                    : "User Type"}
                </h2>
                {(() => {
                  if (viewModal === "totalUsers") {
                    const totalUsers = data?.totalUsers;
                    if (!totalUsers) {
                      return (
                        <p className="text-center text-gray-500 py-4">
                          No data available
                        </p>
                      );
                    }
                    return (
                      <div>
                        <UserBox
                          _id="flexible"
                          count={totalUsers.flexible}
                          label="Flexible"
                          navigateUrl="/dashboard/user-details"
                        />
                        <UserBox
                          _id="outright"
                          count={totalUsers.outright}
                          label="Outright"
                          navigateUrl="/dashboard/user-details"
                        />
                      </div>
                    );
                  }
                  let dataArr: UserData[] = [];
                  if (viewModal === "userCategory") {
                    dataArr = data?.categoryType || [];
                  } else if (viewModal === "userType") {
                    dataArr = data?.userType || [];
                  }
                  return dataArr.length > 0 ? (
                    dataArr.map((user, index) => (
                      <UserBox
                        key={index}
                        _id={user._id}
                        count={user.count}
                        navigateUrl={
                          viewModal === "userCategory"
                            ? "/dashboard/user-category-table"
                            : "/dashboard/user-details"
                        }
                      />
                    ))
                  ) : (
                    <p className="text-center text-gray-500 py-4">
                      No data available
                    </p>
                  );
                })()}
              </section>
            </ModalContent>
          </Modal>
        )}
      </section>
    </section>
  );
};

export default FirstRowMetrics;
