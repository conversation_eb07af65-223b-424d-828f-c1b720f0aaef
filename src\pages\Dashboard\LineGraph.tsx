import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const LineGraph = ({ data, lineData }: any) => {
  const yValues = Array.isArray(data?.growthOverTime?.y)
    ? data.growthOverTime.y
    : [];
  const minY = Math.min(...yValues);
  const maxY = Math.max(...yValues);

  const options: ChartOptions<"line"> = {
    responsive: true,
    plugins: {
      legend: {
        position: "bottom",
        labels: {
          usePointStyle: true,
          pointStyle: "circle",
          boxWidth: 8,
          boxHeight: 8,
          padding: 20,
        },
      },
    },
    elements: {
      point: {
        radius: 0,
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
      },
      y: {
        min: minY,
        max: maxY,
      },
    },
  };

  return (
    <section className="h-[500px]">
      <Line options={options} data={lineData} />
    </section>
  );
};

export default LineGraph;
