import { useNavigate } from "react-router-dom";

const OrderDemo = (data: any) => {
  const navigate = useNavigate();
  const convertedObj = data?.data?.orderDemographicsAllTime;
  const arrData =
    convertedObj &&
    Object.entries(convertedObj || {}).map(([key, value]) => ({
      title: key,
      data: value,
    }));

  return (
    <section className="font-mont">
      <h2 className="font-semibold opacity-45">Order Demographics</h2>

      <section className="bg-white shadow-md rounded-xl py-5 mt-5">
        <article className=" p-2 h-[20rem] overflow-y-auto">
          {arrData ? (
            arrData?.map((item: any, index: number) => (
              <div
                key={index}
                onClick={() =>
                  navigate("/dashboard/order-demographics", {
                    state: { arrData },
                  })
                }
                className="p-3 border rounded-sm flex justify-between items-center hover:bg-[#00B69B1A]"
              >
                <img
                  src="/assets/demoImg.svg"
                  alt="logo"
                  className="w-14 h-14"
                />
                <p className="text-[#979797]">
                  {item.title.match(/times?/i)
                    ? item.title
                    : `${item.title} time orders`}
                </p>
                <p className="opacity-45 font-semibold text-2xl">{item.data}</p>
              </div>
            ))
          ) : (
            <div className="p-3 border rounded-sm flex justify-between items-center hover:bg-[#00B69B1A]">
              <p>No data available</p>
            </div>
          )}
        </article>
      </section>
    </section>
  );
};

export default OrderDemo;
