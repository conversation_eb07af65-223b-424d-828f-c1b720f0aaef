import { useEffect, useState } from "react";
import { getDashboardStats } from "../../redux/thunk";
import {
  Chart as ChartJs,
  Tooltip,
  Legend,
  BarElement,
  ArcElement,
  LinearScale,
  CategoryScale,
} from "chart.js";
import { Pie } from "react-chartjs-2";

ChartJs.register(
  Tooltip,
  Legend,
  ArcElement,
  BarElement,
  LinearScale,
  CategoryScale
);

const PieChart = ({ pieData }: any) => {
  const [data, setData] = useState([]);

  useEffect(() => {
    const getStats = async () => {
      await getDashboardStats().then((res) => {
        setData(res);
      });
    };
    getStats();
  }, []);

  const pieChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
        position: "bottom" as const,
        labels: {
          boxWidth: 20,
          boxHeight: 20,
        },
      },
    },
  };

  return (
    <section className="h-56 w-56 m-auto">
      <Pie options={pieChartOptions} data={pieData} />
    </section>
  );
};

export default PieChart;
