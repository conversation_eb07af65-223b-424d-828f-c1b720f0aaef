import LineGraph from "./LineGraph";

const TotalUsersLineGraph = ({
  isLoading,
  data,
  selectedLineGraphMonth,
  setSelectedLineGraphMonth,
}: any) => {
  const lineData = {
    labels:
      data?.growthOverTime?.x.length > 0
        ? data?.growthOverTime?.x
        : [
            "5k",
            "10k",
            "15k",
            "20k",
            "25k",
            "30k",
            "35k",
            "40k",
            "45k",
            "50k",
            "55k",
            "60k",
          ],
    datasets: [
      {
        label: "Flexible",
        data:
          data?.growthOverTime?.flexible.length > 0
            ? data?.growthOverTime?.flexible
            : [20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20],
        fill: false,
        borderWidth: 1,
        backgroundColor: "#008B50",
        borderColor: "#008B50",
        tension: 0.4,
      },
      {
        label: "Outright",
        data:
          data?.growthOverTime?.outright.length > 0
            ? data?.growthOverTime?.outright
            : [30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],
        fill: true,
        borderWidth: 1,
        backgroundColor: "#00B69B",
        borderColor: "#00B69B",
        tension: 0.4,
      },
    ],
  };

  return (
    <section className="bg-white shadow-md rounded-xl p-5 mb-10">
      <section className="flex items-center justify-between mb-5">
        <article className="inline-flex items-end gap-4 mb-3">
          <h2 className="text-2xl font-semibold font-nunito">Total Users</h2>
          <p className="text-lg text-gray-500 font-mont">
            User Growth Over Time
          </p>
        </article>
        <section>
          <input
            type="month"
            name="lineMonth"
            id="lineMonth"
            className="border text-sm p-1"
            value={selectedLineGraphMonth}
            onChange={(e) => setSelectedLineGraphMonth(e.target.value)}
            disabled={isLoading}
          />
        </section>
      </section>
      <section className={isLoading ? "animate-pulse" : ""}>
        <LineGraph lineData={lineData} data={data} />
      </section>
    </section>
  );
};

export default TotalUsersLineGraph;
