import { formatNumber } from "../../redux/thunk";
import { useNavigate } from "react-router-dom";

const UserBox = ({ /*image, change, */ _id, count, navigateUrl }: any) => {
  const navigate = useNavigate();

  return (
    <section
      className="flex items-center gap-5 my-5 font-nunito cursor-pointer"
      onClick={() => navigate(navigateUrl)}
    >
      <figure className="w-14 h-14">
        <img src={"/assets/userIcon.svg"} alt="" />
      </figure>
      <div>
        <p className="font-semibold text-2xl capitalize">
          {_id === "stateGovernment" ? "state government" : _id}
        </p>

        <section className="flex gap-2 items-center">
          <p className="font-bold text-2xl">{formatNumber(count)}</p>
          {/* <span
            className={`inline-flex text-sm items-center gap-2 ${
              change > 0 ? "text-secondary" : "text-red-400"
            }`}
          >
            {change > 0 ? <FaArrowTrendUp /> : <FaArrowTrendDown />}
            {Math.abs(change)}%
          </span>
          <p className="text-sm">
            {change > 0 ? "Up from last month" : "Down from last month"}
          </p> */}
        </section>
      </div>
    </section>
  );
};

export default UserBox;
