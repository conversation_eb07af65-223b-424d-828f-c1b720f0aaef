import axios from "axios"

export const getTotalUsers = async (year: string, month: string, token: string) => {
  try {
    const res = await axios.get(`${process.env.REACT_APP_API_URL}/getCustomerAnalytics/?year=${year}&month=${month}`, {
      headers: {
        Authorization: token,
      }
    });

    return res.data;
  } catch {
    console.error("Error fetching total users data");
    return {
      totalUsers: 0,
      userType: [],
      categoryType: [],
      growthOverTime: {
        x: [],
        y: [],
        flexible: [],
        outright: [],
      }
    };
  }
}

export const getOrderDemographics = async (year: string, month: string, token: string) => {
  try {
    const res = await axios.get(`${process.env.REACT_APP_API_URL}/getOrderDemographics?year=${year}&month=${month}`, {
      headers: {
        Authorization: token,
      }
    });

    return res.data;
  } catch {
    console.error("Error fetching total users data");
    return {
      orderDemographics:{},
      orderDemographicsAllTime: {}
    };
  }
}

export const getCustomerExpectationReached = async (token: string) => {
  try {
    const res = await axios.get(`${process.env.REACT_APP_API_URL}/getCustomerExpectationReached`, {
      headers: {
        Authorization: token,
      }
    });

    return res.data;
  } catch {
    console.error("Error fetching total users data");
    return {
        "today": 0,
        "thisWeek": 0,
        "thisMonth": 0,
        "thisYear": 0
    }
  }
}

export const getWalletLoanAnalytics = async (token: string) => {
  try {
    const res = await axios.get(`${process.env.REACT_APP_API_URL}/getWalletLoanAnalytics`, {
      headers: {
        Authorization: token,
      }
    });

    return res.data;
  } catch {
    console.error("Error fetching total users data");
    return {
      TotalLoans: 0,
      TotalPaidLoan: 0,
      TotalUnpaidLoan: 0,
      TotalBadLoan: 0,
      monthlyData: {
        approvedLoan: 0,
        totalUnpaidLoan: 0,}
    }
  }
}

