import { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../redux/hooks";
import { getDefaulters, getSingleCustomer } from "../redux/thunk";
import BVNVerificationModal from "../components/modals/BVNVerificationModal";
import { EMandateModal } from "../components/modals/EMandateModal";
import axios from "axios";
import { CardDebit } from "../components/modals/CardDebit";
import { RootState } from "../redux/store";
import { IoSearchOutline } from "react-icons/io5";
import { Preloader } from "../components/elements/Preloader";
import Pagination from "../components/Pagination";
import LoadingSpinner from "../components/elements/LoadingSpinner";
import { FaPhone, FaWhatsapp } from "react-icons/fa6";
import LoanInfo from "../components/modals/LoanInfo";

export const Defaulters = () => {
  const adminLevel = sessionStorage.getItem("adminLevel");
  const [bvnModalOpen, setBvnModalOpen] = useState(false);
  const [filter, setFilter] = useState("all");
  const [selectedBvn, setSelectedBvn] = useState<any>([]);
  const [defaultersList, setDefaultersList] = useState<any>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [emandateModal, setEmandateModal] = useState(false);
  const [defaultAmount, setDefaultAmount] = useState("");
  const [allEmandate, setAllEmandate] = useState<any>([]);
  const [cardDebitModal, setCardDebitModal] = useState(false);
  const [allCards, setAllCards] = useState<any>([]);
  const [searchedUser, setSearchedUser] = useState<any>([]);
  const [searchResults, setSearchResults] = useState<any>([]);
  const [showLoanDetails, setShowLoanDetails] = useState(false);
  const [loanDetails, setLoanDetails] = useState({});
  const [isLoadingEmandate, setIsLoadingEmandate] = useState<{
    [key: number]: Boolean;
  }>({});
  const [acctClick, setAcctClick] = useState({
    monoId: "",
    overdueAmount: "",
    accounts: [],
  });
  const { token } = useAppSelector((store: RootState) => store.auth);

  const fetchMandates = async () => {
    try {
      const res: any = await axios.get(
        `${process.env.REACT_APP_API_URL}/getActiveMandates
        `,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      setAllEmandate(res.data);
    } catch (error) {
      console.error(error);
    }
  };

  const handleShowLoanInfo = (data: any) => {
    setShowLoanDetails(true);
    setLoanDetails(data);
  };

  const dispatch = useAppDispatch();

  const handleBvnClick = (id: any) => {
    dispatch(getSingleCustomer(id)).then((res: any) => {
      setSelectedBvn(res.payload.customer);
      setBvnModalOpen(true);
    });
  };

  const fetchCards = async (id: string) => {
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/getCardDetailsByUserId/${id}
        `,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      setAllCards(res.data.cardDetails);
    } catch (error: any) {
      console.error(error.message);
    }
  };

  const handleCardDebit = async (userId: string, defaultAmount: any) => {
    await fetchCards(userId);
    setCardDebitModal((prev) => !prev);
    setDefaultAmount(defaultAmount);
  };

  useEffect(() => {
    setIsLoading(true);
    (async () => {
      try {
        const response: any = await dispatch(getDefaulters());
        const res = response.payload;
        setDefaultersList(Array.isArray(res) ? res.reverse() : []);
      } finally {
        setIsLoading(false);
      }
    })();
  }, []);

  const handleEmandate = async (
    monoId: string,
    amount: string,
    index: number
  ) => {
    setIsLoadingEmandate((prev) => ({ ...prev, [index]: true }));
    await fetchMandates().then(() =>
      setIsLoadingEmandate((prev) => ({ ...prev, [index]: false }))
    );
    setDefaultAmount(amount);
    setAcctClick((prev) => ({
      ...prev,
      monoId: monoId,
    }));
  };

  useEffect(() => {
    if (acctClick.monoId) {
      const acctArray = allEmandate.filter(
        (acct: any) => acct.customer === acctClick.monoId
      );
      const defaulterArray = defaultersList.filter(
        (acct: any) => acct.monoId === acctClick.monoId
      );

      setAcctClick((prev) => ({
        ...prev,
        accounts: acctArray,
        overdueAmount: defaulterArray[0].OverdueAmount,
      }));

      setEmandateModal((prev) => !prev);
    }
  }, [allEmandate, acctClick.monoId, defaultersList]);

  const searchForUsersWithEmail = (value: string) => {
    setSearchedUser(value);
    if (value.length > 0) {
      const searchResult =
        defaultersList?.filter((user: any) =>
          user.email?.toLowerCase().includes(value.toLowerCase())
        ) || null;
      setSearchResults(searchResult);
    } else {
      setSearchResults([]);
    }
  };

  const [itemsPerPage] = useState(20);
  const [currentPage, setCurrentPage] = useState(1);
  const indexOfLastPost = currentPage * itemsPerPage;
  const indexOfFirstPost = indexOfLastPost - itemsPerPage;
  const currentItems = defaultersList.slice(indexOfFirstPost, indexOfLastPost);

  // Pagination
  const handlePagination = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const prevPage = () => {
    if (currentPage !== 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const nextPage = () => {
    if (currentPage !== Math.ceil(defaultersList.length / itemsPerPage)) {
      setCurrentPage(currentPage + 1);
    }
  };

  const data =
    searchedUser && searchedUser.length > 0 ? searchResults : currentItems;

  return (
    <main className="w-full">
      <div className="bg-white rounded-md shadow-md pb-6">
        <div
          className={`w-full overflow-x-auto ${
            isLoading && "animate-pulse h-[50vh]"
          }`}
        >
          <div
            className="flex items-center justify-between p-6"
            style={{ minWidth: "700px" }}
          >
            <div className="flex justify-between items-center w-full">
              <h1 className="text-base font-semibold ">Defaulters List</h1>
              <div className="relative md:w-[30rem] w-fit">
                <IoSearchOutline className="w-6 h-6 absolute top-[0.6rem] left-2 text-gray-300" />
                <input
                  type="search"
                  name="searchedUser"
                  id="searchedUser"
                  value={searchedUser}
                  onChange={(e) => searchForUsersWithEmail(e.target.value)}
                  placeholder="Search user using email"
                  className="border p-2 rounded-md indent-7 w-full"
                  disabled={defaultersList.length === 0}
                />
              </div>

              <div>
                <label htmlFor="filterOccupation">
                  Sort by:
                  <select
                    name="filterOccupation"
                    id="filterOccupation"
                    className="border p-2 ml-4 text-sm"
                    onChange={(e) => setFilter(e.target.value)}
                  >
                    <option value="all">All</option>
                    <option value="employed">Employed</option>
                    <option value="self-employed">Self-employed</option>
                    <option value="student">Students</option>
                  </select>
                </label>
              </div>
            </div>
          </div>
          <section className="overflow-x-auto">
            <table className="md:w-[1350px] w-[700px] my-3 text-sm text-nowrap">
              <thead className="bg-gray-50 font-bold text-left p-4">
                <tr>
                  <th className="p-2 ">S/N</th>
                  <th className="p-2 ">Full name</th>
                  <th className="p-2">Email</th>
                  <th className="p-2">Phone number</th>
                  <th className="p-2">Contact</th>
                  <th className="p-2">Action</th>
                  <th className="p-2">BVN</th>
                  <th className="p-2">Loan Information</th>
                  {adminLevel === "superadmin" && (
                    <th className="p-2">Action</th>
                  )}
                </tr>
              </thead>
              <tbody>
                {isLoading ? (
                  <tr>
                    <td colSpan={12} className="text-center p-3">
                      <LoadingSpinner />
                    </td>
                  </tr>
                ) : data && data.length > 0 ? (
                  data
                    .filter((data: any) => {
                      if (filter === "employed") {
                        return data.employmentStatus === "employed";
                      } else if (filter === "self-employed") {
                        return data.employmentStatus === "selfEmployed";
                      } else if (filter === "student") {
                        return data.employmentStatus === "student";
                      } else if (filter === "all") {
                        return data.employmentStatus;
                      }
                      return true;
                    })
                    .map((data: any, index: any) => (
                      <tr className="border-b border-gray-300" key={index}>
                        <td className="text-secondary py-4 px-2">
                          {index + indexOfFirstPost + 1}
                        </td>
                        <td className="py-4 px-2">
                          {data.firstName + " " + data.lastName}
                        </td>
                        <td className="py-4 px-2">{data.email}</td>
                        <td className="py-4 px-2">{data.phoneNumber}</td>
                        <td className="p-2 py-4 ">
                          <button
                            type="button"
                            className="mr-3"
                            title="call customer"
                          >
                            <a href={`tel:${data?.phoneNumber}`}>
                              <FaPhone />
                            </a>
                          </button>
                          <button type="button" title="text via whatsapp">
                            <a
                              href={`https://wa.me/${data?.phoneNumber?.replace(
                                /^0/,
                                "234"
                              )}`}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <FaWhatsapp />
                            </a>
                          </button>
                        </td>

                        <td className="py-4 px-2">
                          <button
                            disabled={
                              data.cards === "No cards attached to this user"
                            }
                            onClick={() =>
                              handleCardDebit(data.userId, data.defaultAmount)
                            }
                            className={`${
                              data.cards === "No cards attached to this user"
                                ? "bg-gray-200 text-black"
                                : "bg-secondary text-white"
                            } p-2 text-sm rounded-md`}
                          >
                            {data.cards === "No cards attached to this user"
                              ? "No Card"
                              : "Card debit"}
                          </button>
                        </td>
                        <td className="py-4 px-2">
                          <button
                            onClick={() => handleBvnClick(data.userId)}
                            className="text-blue-500 hover:underline focus:outline-none"
                          >
                            {data.bvn}
                          </button>
                        </td>
                        <td className="py-4 px-2 flex justify-center items-center">
                          <button
                            type="button"
                            className="text-secondary text-sm"
                            onClick={() => handleShowLoanInfo(data)}
                          >
                            View
                          </button>
                        </td>
                        {adminLevel === "superadmin" && (
                          <td className="py-4 px-2">
                            <button
                              type="button"
                              className="bg-secondary text-white text-sm rounded-md p-2 w-40"
                              onClick={() =>
                                handleEmandate(
                                  data.monoId,
                                  data.defaultAmount,
                                  index
                                )
                              }
                            >
                              {isLoadingEmandate[index] ? (
                                <Preloader />
                              ) : (
                                "e-mandate debit"
                              )}
                            </button>
                          </td>
                        )}
                      </tr>
                    ))
                ) : (
                  <tr>
                    <td colSpan={12} className="text-center p-3 text-secondary">
                      {searchedUser.length > 0
                        ? "User not found"
                        : "No defaulters found"}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </section>
        </div>
        <section className="p-3 my-5">
          <Pagination
            length={
              searchedUser.length > 0
                ? searchResults.length
                : defaultersList.length
            }
            itemsPerPage={itemsPerPage}
            handlePagination={handlePagination}
            currentPage={currentPage}
            prevPage={prevPage}
            nextPage={nextPage}
          />
        </section>
      </div>
      {showLoanDetails && (
        <LoanInfo
          loanDetails={loanDetails}
          closeModal={() => setShowLoanDetails(false)}
        />
      )}
      {emandateModal && (
        <EMandateModal
          setEMandateModal={setEmandateModal}
          defaultAmount={defaultAmount}
          acctClick={acctClick}
        />
      )}
      {bvnModalOpen && (
        <BVNVerificationModal
          setBvnModalOpen={setBvnModalOpen}
          selectedBvn={selectedBvn}
          acctClick={acctClick}
        />
      )}
      {cardDebitModal && (
        <CardDebit
          defaultAmount={defaultAmount}
          allCards={allCards}
          setCardDebitModal={setCardDebitModal}
        />
      )}
    </main>
  );
};
