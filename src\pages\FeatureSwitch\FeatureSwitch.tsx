import { useEffect, useState } from "react";
import Modal, { ModalContent } from "../../components/elements/Modal";
import axios from "axios";
import { useAppDispatch, useAppSelector } from "../../redux/hooks";
import LoadingSpinner from "../../components/elements/LoadingSpinner";
import toast from "react-hot-toast";
import { fetchAllCategories } from "../../redux/thunk";
import ToggleList from "./ToggleList";

const FeatureSwitch = () => {
  const [confirmModal, setConfirmModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [allCategories, setAllCategories] = useState<any[]>([]);
  const [buyNowPayLaterToggle, setBuyNowPayLaterToggle] = useState<{
    _id: string;
    isActive: boolean;
  }>({ _id: "", isActive: false });
  const { token } = useAppSelector((store) => store.auth);
  const dispatch = useAppDispatch();

  useEffect(() => {
    getBuyNowPayLaterToggle();
    dispatchAllCategories();
  }, []);

  const dispatchAllCategories = () => {
    dispatch(fetchAllCategories()).then((res) => {
      if (res.meta.requestStatus === "fulfilled") {
        setAllCategories(res.payload);
      } else {
        console.error("Failed to fetch categories");
      }
    });
  };

  const getBuyNowPayLaterToggle = async () => {
    setIsLoading(true);
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/getBuyNowPayLaterToggle`,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      setBuyNowPayLaterToggle(res.data.data);
    } catch (error) {
      console.error("Error fetching toggles:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateBuyNowPayLaterToggle = async () => {
    setIsLoading(true);
    try {
      const res = await axios.post(
        `${process.env.REACT_APP_API_URL}/createBuyNowPayLaterToggle`,
        {},
        {
          headers: {
            Authorization: token,
          },
        }
      );
      toast.success(res.data.message || "Toggle created successfully!");
      getBuyNowPayLaterToggle(); // Refresh the toggles after creation
    } catch (error) {
      console.error("Error creating toggle:", error);
    } finally {
      setIsLoading(false);
      setConfirmModal(false);
    }
  };

  const handleBuyNowToggleChange = async () => {
    setIsLoading(true);
    try {
      await axios.put(
        `${process.env.REACT_APP_API_URL}/toggleBuyNowPayLater/${buyNowPayLaterToggle._id}`,
        {},
        {
          headers: {
            Authorization: token,
          },
        }
      );
      toast.success("Buy Now Pay Later Toggle updated successfully!");
      getBuyNowPayLaterToggle(); // Refresh the toggles after update
    } catch (error) {
      console.error("Error updating toggle:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <section className="p-6 bg-white rounded-lg shadow">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Feature Switches</h2>
        <button
          type="button"
          title="Create a new feature toggle"
          onClick={() => setConfirmModal(true)}
          disabled={isLoading || !!buyNowPayLaterToggle._id}
          className={`${
            isLoading || !!buyNowPayLaterToggle._id
              ? "opacity-50 cursor-not-allowed"
              : "hover:bg-secondary hover:text-white"
          } bg-secondary text-white font-medium px-4 py-2 rounded transition`}
        >
          + Create Buy Now Toggle
        </button>
      </div>
      <p className="text-gray-600 mb-4">
        Manage feature toggles for the application.
      </p>
      <div className="bg-gray-50 rounded-lg p-4">
        {isLoading ? (
          <div className="flex justify-center py-8">
            <LoadingSpinner />
          </div>
        ) : (
          <ul className="divide-y divide-gray-200">
            {buyNowPayLaterToggle._id ? (
              <>
                <li className="flex items-center justify-between py-3">
                  <span className="font-medium text-gray-800">
                    Buy Now Pay Later Toggle
                  </span>
                  <label className="switch">
                    <input
                      type="checkbox"
                      checked={buyNowPayLaterToggle.isActive}
                      onChange={handleBuyNowToggleChange}
                    />
                    <span className="slider round"></span>
                  </label>
                </li>
                {allCategories.length > 0 ? (
                  allCategories.map((category) => (
                    <ToggleList
                      key={category._id}
                      category={category}
                      refresh={() => {
                        getBuyNowPayLaterToggle();
                        dispatchAllCategories();
                      }}
                    />
                  ))
                ) : (
                  <li className="text-gray-500 text-center py-6">
                    No categories available.
                  </li>
                )}
              </>
            ) : (
              <li className="text-gray-500 text-center py-6">
                No feature toggles available.
              </li>
            )}
          </ul>
        )}
      </div>
      {confirmModal && (
        <Modal
          open={confirmModal}
          onClose={() => setConfirmModal(false)}
          className="flex items-center justify-center"
        >
          <ModalContent className="mx-4 p-8 rounded-lg shadow-2xl flex flex-col gap-6 items-center bg-white w-full max-w-md">
            <div className="w-12 h-12 flex items-center justify-center bg-gray-100 rounded-full mb-2">
              <svg
                className="w-6 h-6 text-secondary"
                fill="none"
                stroke="currentColor"
                strokeWidth={2}
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M13 16h-1v-4h-1m1-4h.01M12 20a8 8 0 100-16 8 8 0 000 16z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-800 text-center">
              Create New Feature Toggle?
            </h3>
            <p className="text-gray-600 text-center">
              Are you sure you want to create a new feature toggle? This action
              can be undone later.
            </p>
            <div className="flex gap-4 w-full justify-center mt-2">
              <button
                type="button"
                className="bg-secondary hover:bg-[#227150] text-white font-medium px-5 py-2 rounded transition"
                disabled={isLoading}
                aria-label="Confirm create toggle"
                onClick={handleCreateBuyNowPayLaterToggle}
              >
                Confirm
              </button>
              <button
                type="button"
                className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium px-5 py-2 rounded transition"
                onClick={() => setConfirmModal(false)}
              >
                Cancel
              </button>
            </div>
          </ModalContent>
        </Modal>
      )}
    </section>
  );
};

export default FeatureSwitch;
