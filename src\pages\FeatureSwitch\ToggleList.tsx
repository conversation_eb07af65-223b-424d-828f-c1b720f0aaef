import axios from "axios";
import { useAppSelector } from "../../redux/hooks";
import toast from "react-hot-toast";

const ToggleList = ({ category, refresh }: any) => {
  const { token } = useAppSelector((store) => store.auth);

  const handleToggle = async (id: string) => {
    try {
      await axios.patch(
        `${process.env.REACT_APP_API_URL}/toggleIsPublish/${id}`,
        {},
        {
          headers: {
            Authorization: token,
          },
        }
      );
      toast.success("Category toggled successfully");
      refresh();
    } catch (error) {
      console.error("Error toggling category:", error);
    }
  };

  return (
    <li className="flex items-center justify-between py-4">
      <span className="font-medium text-gray-800">
        {category.category} Toggle
      </span>
      <label className="switch">
        <input
          type="checkbox"
          checked={category.isPublish}
          onChange={() => handleToggle(category._id)}
        />
        <span className="slider round"></span>
      </label>
    </li>
  );
};

export default ToggleList;
