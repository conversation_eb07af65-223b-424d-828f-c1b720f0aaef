import { useEffect, useState } from "react";
import NotificationCard from "../components/NotificationCard";
import { IoMdClose } from "react-icons/io";
import { useAppSelector, useAppDispatch } from "../redux/hooks";
import { RootState } from "../redux/store";
import { markAsRead, markAllAsRead } from "../redux/notifications/notificationSlice";

const Notification = () => {
  const dispatch = useAppDispatch();
  const { notifications } = useAppSelector((state: RootState) => state.notifications);
  const [activeTab, setActiveTab] = useState("all");
  const [filteredNotification, setFilteredNotification] = useState(notifications);

  const [activeNotificationId, setActiveNotificationId] = useState<
    null | number
  >(null);

  const handleMoreClick = (index: number) => {
    setActiveNotificationId((prev) => (prev === index ? null : index));
  };

  const handleOutsideClick = () => {
    setActiveNotificationId(null);
  };

  useEffect(() => {
    const filterNotifications = () => {
      if (activeTab === "all") {
        setFilteredNotification(notifications);
      } else {
        // Map notification types to tab names
        const typeMap: { [key: string]: string } = {
          "user": "signUp",
          "order": "orders",
          "payment": "payments"
        };

        const filtered = notifications.filter((item) => {
          const mappedType = typeMap[item.type] || item.type;
          return mappedType === activeTab;
        });
        setFilteredNotification(filtered);
      }
    };
    filterNotifications();
  }, [activeTab, notifications]);

  const handleMarkAsRead = (index: number) => {
    const notification = filteredNotification[index];
    if (notification) {
      dispatch(markAsRead(notification.id));
    }
  };

  const handleMarkAsUnread = (index: number) => {
    // For now, we don't have an unread action in our slice
    // You can add it if needed
  };

  const handleMarkAllAsRead = () => {
    dispatch(markAllAsRead());
  };

  // Helper function to format date
  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);

    // For very recent notifications, show relative time
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;

    // For older notifications, show readable date format
    const options: Intl.DateTimeFormatOptions = {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    };

    return date.toLocaleDateString('en-US', options);
  };

  return (
    <section className="bg-white py-5 px-7" onClick={()=>setActiveNotificationId(null)}>
      <div className="flex items-center justify-between px-4">
        <h3 className="font-bold text-lg">Notifications</h3>
        <button
          type="button"
          className="text-secondary text-sm flex items-center gap-3 cursor-pointer"
          onClick={() => handleMarkAllAsRead()}
        >
          Mark all as read
          <span>
            <IoMdClose className="w-5 h-5 text-gray-400" />
          </span>
        </button>
      </div>
      <section className="py-3 border-b">
        <ul className="flex items-center gap-6">
          <li
            className={`${
              activeTab === "all" && "border-b-2 border-secondary"
            } px-4 py-1 text-sm cursor-pointer`}
            onClick={() => setActiveTab("all")}
          >
            All
          </li>
          <li
            className={`${
              activeTab === "signUp" && "border-b-2 border-secondary"
            } px-4 py-1 text-sm cursor-pointer`}
            onClick={() => setActiveTab("signUp")}
          >
            Sign-up
          </li>
          <li
            className={`${
              activeTab === "admin" && "border-b-2 border-secondary"
            } px-4 py-1 text-sm cursor-pointer`}
            onClick={() => setActiveTab("adminActivities")}
          >
            Admin Activities
          </li>
          <li
            className={`${
              activeTab === "orders" && "border-b-2 border-secondary"
            } px-4 py-1 text-sm cursor-pointer`}
            onClick={() => setActiveTab("orders")}
          >
            Orders
          </li>
          <li
            className={`${
              activeTab === "payments" && "border-b-2 border-secondary"
            } px-4 py-1 text-sm cursor-pointer`}
            onClick={() => setActiveTab("payments")}
          >
            Payments
          </li>
        </ul>
      </section>
      <section>
        {filteredNotification.length > 0 ? (
          filteredNotification.map((item: any, index: number) => (
            <NotificationCard
              key={item.id}
              index={index}
              img="/assets/Avatar.png" // Default avatar for now
              name={item.title}
              msg={item.message}
              dateTime={formatDate(item.createdAt)}
              readStatus={item.read}
              setReadMsg={() => handleMarkAsRead(index)}
              setUnreadMsg={() => handleMarkAsUnread(index)}
              activeNotificationId={activeNotificationId}
              setActiveNotificationId={setActiveNotificationId}
            />
          ))
        ) : (
          <p className="text-secondary text-center py-20">No Notifications</p>
        )}
      </section>
    </section>
  );
};

export default Notification;
