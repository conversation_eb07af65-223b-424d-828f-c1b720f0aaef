import axios from "axios";
import { useEffect, useState } from "react";
import { useAppSelector } from "../redux/hooks";
import { RootState } from "../redux/store";
import LoadingSpinner from "../components/elements/LoadingSpinner";
import { useLocation } from "react-router-dom";
import Pagination from "../components/Pagination";

const OrderDemographics = () => {
  const [data, setData] = useState<any>([]);
  const [filteredData, setFilteredData] = useState<any>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { token } = useAppSelector((store: RootState) => store.auth);
  const [selectedTimeRange, setSelectedTimeRange] = useState("1-time");
  const [accountTypeFilter, setAccountTypeFilter] = useState("");
  const [categoryTypeFilter, setCategoryTypeFilter] = useState("");
  const location = useLocation();
  const { state } = location;
  const timeRange = state?.arrData || [];

  const mainData =
    accountTypeFilter || categoryTypeFilter ? filteredData : data;
  const [itemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const indexOfLastPost = currentPage * itemsPerPage;
  const indexOfFirstPost = indexOfLastPost - itemsPerPage;
  const currentItems = mainData.slice(indexOfFirstPost, indexOfLastPost);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const response = await axios.get(
          `${process.env.REACT_APP_API_URL}/getUsersByOrderFrequency?range=${selectedTimeRange}`,
          {
            headers: {
              Authorization: token,
            },
          }
        );

        setData(response.data.users);
      } catch (error) {
        console.error("Error fetching order demographics:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [selectedTimeRange, token]);

  // Pagination
  const handlePagination = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const prevPage = () => {
    if (currentPage !== 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const nextPage = () => {
    if (currentPage !== Math.ceil(data.length / itemsPerPage)) {
      setCurrentPage(currentPage + 1);
    }
  };

  useEffect(() => {
    if (accountTypeFilter || categoryTypeFilter) {
      const filteredData = data.filter(
        (user: any) =>
          user.accountType?.toLowerCase() === accountTypeFilter.toLowerCase() ||
          user.categoryType?.toLowerCase() === categoryTypeFilter.toLowerCase()
      );
      setFilteredData(filteredData);
    } else {
      setFilteredData(data);
    }
  }, [data, accountTypeFilter, categoryTypeFilter]);

  return (
    <section className="font-mont">
      <section className="flex justify-between items-center mb-5">
        <h2>Order Demographics</h2>
        <span className="flex gap-3 text-sm">
          <select
            name="filter"
            id="filter"
            className="border p-2"
            onChange={(e) => {
              setCategoryTypeFilter(e.target.value);
              setAccountTypeFilter("");
            }}
            value={categoryTypeFilter}
          >
            <option value="">Filter by Category Type</option>
            <option value="individual">Individuals</option>
            <option value="stateGovernment">State Government</option>
          </select>
          <select
            name="filter"
            id="filter"
            className="border p-2"
            onChange={(e) => {
              setAccountTypeFilter(e.target.value);
              setCategoryTypeFilter("");
            }}
            value={accountTypeFilter}
          >
            <option value="">Filter by Account Type</option>
            <option value="flexible">Flexible Users</option>
            <option value="outright">Outright Users</option>
          </select>
        </span>
      </section>
      <section className="flex gap-5">
        <ul className="bg-white p-5 shadow-md rounded-md w-1/5">
          {timeRange.map((range: any, index: number) => (
            <li
              className={`${
                selectedTimeRange === range.title
                  ? "bg-gray-200"
                  : "hover:bg-[#00B69B1A]"
              } p-4 transition-colors duration-200`}
              key={index}
            >
              <button
                type="button"
                className={`w-full text-left p-2  text-sm`}
                onClick={() => setSelectedTimeRange(range.title)}
              >
                {range.title} Order
              </button>
            </li>
          ))}
        </ul>
        <section className="w-4/5">
          <section className="overflow-x-auto">
            <table className="w-[1000px]" style={{ minWidth: "700px" }}>
              <thead>
                <tr className="border rounded-t-lg">
                  <th className="p-2">SN</th>
                  <th className="p-2">Name</th>
                  <th className="p-2">Email Address</th>
                  <th className="p-2">Category Type</th>
                  <th className="p-2">Account Type</th>
                  <th className="p-2">Transaction History</th>
                </tr>
              </thead>
              <tbody className="text-sm">
                {isLoading ? (
                  <tr>
                    <td colSpan={5} className="text-center p-4">
                      <LoadingSpinner />
                    </td>
                  </tr>
                ) : currentItems && currentItems.length > 0 ? (
                  currentItems.map((user: any, index: number) => (
                    <tr className="border-b" key={index}>
                      <td className="p-4 text-center">{index + 1}</td>
                      <td className="p-4 text-center capitalize">{`${user.firstName.toLowerCase()} ${user.lastName.toLowerCase()}`}</td>
                      <td className="p-4 text-center">{user.email}</td>
                      <td className="p-4 text-center">
                        {user.categoryType || "---"}
                      </td>
                      <td className="p-4 text-center">
                        {user.accountType || "---"}
                      </td>
                      <td className="p-4 text-center text-secondary">View</td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={5} className="text-center p-2">
                      No data available
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </section>
          <section className="p-3 my-5">
            <Pagination
              length={mainData.length}
              itemsPerPage={itemsPerPage}
              handlePagination={handlePagination}
              currentPage={currentPage}
              prevPage={prevPage}
              nextPage={nextPage}
            />
          </section>
        </section>
      </section>
    </section>
  );
};

export default OrderDemographics;
