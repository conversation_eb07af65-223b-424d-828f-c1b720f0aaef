import { FiMessageCircle } from "react-icons/fi";
import { CiPhone } from "react-icons/ci";
import { BiX } from "react-icons/bi";
import { FaStar } from "react-icons/fa";
import { formatNumber } from "../../redux/thunk";

interface ProgressStepProps {
  label: string;
  isCompleted: boolean;
  isCurrent: boolean;
}

const ProgressStep: React.FC<ProgressStepProps> = ({
  label,
  isCompleted,
  isCurrent,
}) => (
  <div className="relative flex flex-col items-center w-20 md:w-24 text-center">
    <div
      className={`w-6 h-6 rounded-full ${
        isCompleted ? "bg-[#008B50]" : "bg-gray-300"
      } flex items-center justify-center z-10 border-2 ${
        isCompleted ? "border-white" : "border-transparent"
      }`}
    >
      {isCompleted && <div className="w-2 h-2 bg-white rounded-full"></div>}
    </div>
    <p
      className={`mt-1.5 text-xs font-medium ${
        isCurrent || isCompleted ? "text-[#008B50]" : "text-gray-500"
      }`}
    >
      {label}
    </p>
  </div>
);

export default function DeliverySummaryModal({ selectedOrder, onClose }: any) {
  const getProgressState = (statusObj: string) => {
    const steps = ["pending", "outForDelivery", "delivered"] as const;
    const currentStepIndex = steps.indexOf(statusObj as (typeof steps)[number]);
    return { currentStepIndex };
  };

  const progressData = selectedOrder
    ? getProgressState(selectedOrder?.status)
    : { currentStepIndex: -1 };
  const progressLineWidthPercent =
    progressData.currentStepIndex > 0
      ? (progressData.currentStepIndex / 3) * 100
      : 0;

  // --- Main Modal Content ---
  return (
    <div
      className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <div
        className="bg-white rounded-xl shadow-xl p-6 md:p-8 max-w-xl w-full relative my-auto max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100 transition z-20"
          aria-label="Close modal"
        >
          <BiX size={26} />
        </button>

        <div className="mb-8 pt-4">
          <div className="relative flex justify-between w-full px-1">
            <div className="absolute top-[11px] left-[calc(12.5%+12px)] right-[calc(12.5%+12px)] h-[3px] bg-gray-200" />
            <div
              className="absolute top-[11px] left-[calc(12.5%+12px)] h-[3px] bg-[#008B50] transition-all duration-500 ease-out"
              style={{ width: `${progressLineWidthPercent}%` }}
            />

            {/* Render each step */}
            <ProgressStep
              label="Order Pending"
              isCompleted={progressData.currentStepIndex >= 0}
              isCurrent={progressData.currentStepIndex === 0}
            />
            <ProgressStep
              label="Out For Delivery"
              isCompleted={progressData.currentStepIndex >= 1}
              isCurrent={progressData.currentStepIndex === 1}
            />
            <ProgressStep
              label="Delivered"
              isCompleted={progressData.currentStepIndex >= 2}
              isCurrent={progressData.currentStepIndex === 2}
            />
          </div>
        </div>

        {/* --- Delivery Address --- */}
        <div className="mb-6 md:mb-8">
          <h3 className="text-base md:text-lg font-semibold text-gray-800 mb-2">
            Delivery
          </h3>
          <p className="text-xs text-gray-500 mb-1">Address</p>
          {/* Changed address text color slightly */}
          <p className="text-sm text-gray-700 leading-snug">
            {selectedOrder?.deliveryDetails || "No address provided"},
          </p>
        </div>

        {/* --- Driver Information --- */}
        <div className="flex items-center justify-between mb-6 md:mb-8">
          <div className="flex items-center">
            <img
              src={selectedOrder?.riderId?.image || "/placeholder-driver.png"}
              alt={selectedOrder?.riderId?.firstName}
              className="w-14 h-14 rounded-full object-cover mr-4 border border-gray-200"
            />
            <div>
              <p className="text-base font-semibold text-gray-800">
                {selectedOrder?.riderId?.firstName +
                  " " +
                  selectedOrder?.riderId?.lastName || "-- --"}
              </p>
              <p className="text-sm text-gray-500">
                {selectedOrder?.riderId?.bikeModel_Color || "-- --"}
              </p>
            </div>
          </div>
          <div className="flex flex-col items-end">
            <div className="flex items-center mb-0.5">
              <span className="text-base font-bold mr-1">
                {selectedOrder?.riderId?.rating?.toFixed(1) || "0.0"}
              </span>
              <FaStar className="w-4 h-4 text-yellow-400" />
            </div>
            <p className="text-xs text-gray-500">
              {selectedOrder?.riderId?.vehiclePlate || "--"}
            </p>
          </div>
        </div>

        {/* --- Contact Buttons --- */}
        <section>
          <p className="text-center mb-2 ">
            {selectedOrder?.riderId?.phoneNumber || "080..."}
          </p>

          <div className="flex justify-center space-x-4 mb-6 md:mb-8">
            <button
              title="Message Driver"
              className="w-11 h-11 bg-[#008B50] rounded-full flex items-center justify-center text-white shadow hover:bg-[#007A48] transition duration-150 ease-in-out"
            >
              <a
                href={`https://wa.me/${selectedOrder?.riderId?.phoneNumber?.replace(
                  /^0/,
                  "234"
                )}`}
                target="_blank"
                rel="noopener noreferrer"
              >
                <FiMessageCircle size={20} />
              </a>
            </button>
            <button
              title="Call Driver"
              className="w-11 h-11 bg-[#008B50] rounded-full flex items-center justify-center text-white shadow hover:bg-[#007A48] transition duration-150 ease-in-out"
            >
              <a href={`tel:${selectedOrder?.riderId?.phoneNumber}`}>
                <CiPhone size={22} />
              </a>
            </button>
          </div>
        </section>

        {/* --- Order Summary --- */}
        <div>
          <h3 className="text-base md:text-lg font-semibold text-gray-800 mb-3">
            Order Summary
          </h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Subtotal</span>
              {/* Displaying Subtotal as it is in the data, comment out if not needed */}
              <span className="font-medium text-gray-800">
                ₦{" "}
                {selectedOrder?.subtotal
                  ? formatNumber(selectedOrder.subtotal)
                  : "0.00"}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Interest</span>
              <span className="font-medium text-gray-800">
                ₦
                {selectedOrder?.interest
                  ? formatNumber(selectedOrder.interest)
                  : "0.00"}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Delivery Fee</span>
              <span className="font-medium text-gray-800">
                ₦{" "}
                {selectedOrder?.deliveryFee
                  ? formatNumber(selectedOrder.deliveryFee)
                  : "0.00"}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Service Fee</span>
              <span className="font-medium text-gray-800">
                + ₦{" "}
                {selectedOrder?.serviceFee
                  ? formatNumber(selectedOrder.serviceFee)
                  : "0.00"}
              </span>
            </div>
            <div className="border-t border-gray-200 pt-3 mt-3">
              <div className="flex justify-between font-bold text-base text-gray-900">
                <span>Total</span>
                <span>
                  ₦
                  {selectedOrder?.allItemsTotalPrice
                    ? formatNumber(selectedOrder?.allItemsTotalPrice)
                    : "0.00"}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
