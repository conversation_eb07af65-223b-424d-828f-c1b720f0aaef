import { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../redux/hooks";
import { approveOrder, getOrders } from "../../redux/thunk";
import toast from "react-hot-toast";
import LoadingSpinner from "../../components/elements/LoadingSpinner";
import OrderDetails from "../../components/modals/OrderDetails";
import axios from "axios";
import { RootState } from "../../redux/store";
import Pagination from "../../components/Pagination";
import ViewCard from "../../components/modals/ViewCard";
import { FaRegCopy } from "react-icons/fa6";
import { Preloader } from "../../components/elements/Preloader";
import { IoClose, IoSearchOutline } from "react-icons/io5";
import DeliverySummaryModal from "../OrderTracking/DeliverySummaryModal";
import { formatDate } from "../../components/micro/Calender";

export const Order = () => {
  const [activeTab, setActiveTab] = useState("flexible");
  const [viewDetails, setViewDetails] = useState(false);
  const [viewCancelOrder, setViewCancelOrder] = useState({
    details: {},
    status: false,
    btn: false,
  });
  const [cancelReason, setCancelReason] = useState("");
  const [orderDetails, setOrderDetails] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);
  const [pickupDetails, setPickupDetails] = useState<any>([]);
  const [statusFilter, setStatusFilter] = useState("");
  const [deliveryDateFilter, setDeliveryDateFilter] = useState<string>("");
  const [error, setError] = useState<any>({});
  const [ordersWithKnownUsers, setOrdersWithKnownUsers] = useState([]);
  const [cardDetails, setCardDetails] = useState([]);
  const [showCard, setShowCard] = useState(false);
  const [searchedUser, setSearchedUser] = useState("");
  const { token } = useAppSelector((store: RootState) => store.auth);
  const [selectedOrder, setSelectedOrder] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [selectedDate, setSelectedDate] = useState("");

  const dispatch = useAppDispatch();
  const { orders, status } = useAppSelector((store) => store.orders as any);

  const [itemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const indexOfLastPost = currentPage * itemsPerPage;
  const indexOfFirstPost = indexOfLastPost - itemsPerPage;

  useEffect(() => {
    dispatch(getOrders());
  }, [dispatch]);

  useEffect(() => {
    if (orders.length > 0)
      setOrdersWithKnownUsers(
        orders.filter(
          (order: any) =>
            order.userId && order.userId.categoryType !== "stateGovernment"
        )
      );
  }, [orders]);

  useEffect(() => {
    // Reset pagination when filters change
    setCurrentPage(1);
  }, [activeTab, statusFilter, deliveryDateFilter, searchedUser]);

  const validateOrderApproval = () => {
    let isValid = true;
    let newError: any = {};

    pickupDetails.forEach((item: any) => {
      if (item.vendorId.length === 0) {
        newError.pickupDetails = "Please select vendor for all items";
        isValid = false;
      }
    });
    setError(newError);
    return isValid;
  };

  const handleCopyClick = (email: string) => {
    if (!email) return;
    navigator.clipboard.writeText(email);
    toast.success("Email copied to clipboard");
  };

  const handleInitialApprove = async (id: string, pickUpDetails: string) => {
    setError({});
    if (pickUpDetails.trim().length === 0) {
      setError({ initialPickup: "Enter pickup location" });
      return;
    }

    const payload = {
      orderId: id,
      pickUpDetails,
    };

    setIsLoading(true);
    try {
      const res = await axios.post(
        `${process.env.REACT_APP_API_URL}/approveOrderInitial`,
        payload,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      toast.success(res.data.message || "Order approved successfully");
      dispatch(getOrders());
      setViewDetails(false);
    } catch (error: any) {
      console.error("Initial Approve Error:", error);
      toast.error(
        error.response?.data?.message ||
          error.message ||
          "Failed to approve order!"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewCard = (cardDetails: any) => {
    setCardDetails(cardDetails);
    setShowCard(true);
  };

  const handleApproveOrder = async (id: string) => {
    if (!validateOrderApproval()) {
      return;
    }

    setIsLoading(true);
    try {
      await dispatch(
        approveOrder({ orderId: id, pickUpDetails: pickupDetails })
      ).unwrap();

      toast.success("Order approved successfully!");
      dispatch(getOrders());
      setViewDetails(false);
    } catch (error: any) {
      console.error("Approve Order Error:", error);
      toast.error(error?.message || "Failed to approve order!");
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewDetails = (orderData: any) => {
    setOrderDetails(orderData);
    setViewDetails(true);
    setError({});
  };

  const getFilteredAndSearchedOrders = () => {
    const isTabMatch = (order: any): boolean => {
      if (activeTab === "comboPack") return order.isXmasPackage;
      if (activeTab === "flexible" && !order.isXmasPackage)
        return order.userId?.accountType === "flexible";
      if (activeTab === "outright" && !order.isXmasPackage)
        return order.userId?.accountType === "outright";
      return false;
    };

    const isDeliveryDateMatch = (order: any): boolean => {
      if (!deliveryDateFilter) return true;

      const deliveryDateOption =
        order.userId?.deliveryDateOption || order.deliveryDateOption;
      if (!deliveryDateOption) return false;

      const deliveryDate = new Date(deliveryDateOption);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (deliveryDateFilter === "today") {
        deliveryDate.setHours(0, 0, 0, 0);
        return deliveryDate.getTime() === today.getTime();
      }

      const days = parseInt(deliveryDateFilter.replace("day", ""), 10);
      if (!isNaN(days)) {
        const targetDate = new Date();
        targetDate.setDate(today.getDate() + days);
        targetDate.setHours(23, 59, 59, 999);
        return deliveryDate <= targetDate;
      }

      return true;
    };

    const isStatusMatch = (order: any): boolean => {
      if (!statusFilter) return true;
      return order.status === statusFilter;
    };

    const isSearchMatch = (order: any): boolean => {
      if (!searchedUser) return true;
      return order.userId?.email
        ?.toLowerCase()
        .includes(searchedUser.toLowerCase());
    };

    const isDateMatch = (order: any): boolean => {
      if (!selectedDate) return true;

      const orderDate = new Date(order.createdAt);
      const orderYear = orderDate.getFullYear();
      const orderMonth = String(orderDate.getMonth() + 1).padStart(2, "0");
      const orderDay = String(orderDate.getDate()).padStart(2, "0");
      const orderKey = `${orderYear}-${orderMonth}-${orderDay}`;

      return orderKey === selectedDate;
    };

    const filteredOrders = ordersWithKnownUsers
      .filter((order) => isTabMatch(order))
      .filter((order) => isDeliveryDateMatch(order))
      .filter((order) => isStatusMatch(order))
      .filter((order) => isSearchMatch(order))
      .filter((order) => isDateMatch(order))
      .reverse();

    return filteredOrders;
  };

  const finalFilteredOrders = getFilteredAndSearchedOrders();

  // --- Pagination Logic using finalFilteredOrders ---
  const currentItems = finalFilteredOrders.slice(
    indexOfFirstPost,
    indexOfLastPost
  );

  const handleCancelOrder = async (orderId: string) => {
    if (cancelReason.trim().length === 0) {
      toast.error("Please provide a reason for cancellation.");
      return;
    }

    setViewCancelOrder((prev) => ({
      ...prev,
      btn: true,
    }));

    const payload = {
      cancelReason: cancelReason,
    };

    try {
      const res = await axios.post(
        `${process.env.REACT_APP_API_URL}/cancelOrder/order/${orderId}`,
        payload,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      toast.success(res.data.message || "Order canceled successfully");
      dispatch(getOrders());
      setViewCancelOrder({ details: {}, status: false, btn: false });
      setCancelReason("");
    } catch (error: any) {
      console.error("Cancel Order error", error);
      toast.error(
        error.response?.data?.message ||
          error.message ||
          "Failed to cancel order"
      );
      setViewCancelOrder((prev) => ({
        ...prev,
        btn: false,
      }));
    }
  };

  const handlePagination = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const prevPage = () => {
    setCurrentPage((prev) => (prev > 1 ? prev - 1 : prev));
  };

  const nextPage = () => {
    setCurrentPage((prev) =>
      prev < Math.ceil(finalFilteredOrders.length / itemsPerPage)
        ? prev + 1
        : prev
    );
  };

  const handleTrackNow = (order: any) => {
    setSelectedOrder(order);
    setShowModal(true);
  };

  return (
    <main>
      <h2 className="font-semibold text-lg mb-4">Clients' Orders</h2>
      <section className="my-6 flex flex-wrap items-center justify-between gap-4">
        <div className="flex items-center gap-5 border-b border-gray-200 pb-2">
          <span
            className={`cursor-pointer text-sm sm:text-base pb-2 ${
              activeTab === "flexible"
                ? "border-b-2 border-secondary font-semibold text-secondary"
                : "text-gray-600 hover:text-secondary"
            }`}
            onClick={() => setActiveTab("flexible")}
          >
            Flexible Orders
          </span>
          <span
            className={`cursor-pointer text-sm sm:text-base pb-2 ${
              activeTab === "outright"
                ? "border-b-2 border-secondary font-semibold text-secondary"
                : "text-gray-600 hover:text-secondary"
            }`}
            onClick={() => setActiveTab("outright")}
          >
            Outright Orders
          </span>
          <span
            className={`cursor-pointer text-sm sm:text-base pb-2 ${
              activeTab === "comboPack"
                ? "border-b-2 border-secondary font-semibold text-secondary"
                : "text-gray-600 hover:text-secondary"
            }`}
            onClick={() => setActiveTab("comboPack")}
          >
            Combo Pack
          </span>
        </div>
      </section>
      <section className="flex items-center justify-between flex-wrap gap-4">
        <div className="relative">
          <IoSearchOutline className="w-5 h-5 absolute top-1/2 left-3 transform -translate-y-1/2 text-gray-400" />
          <input
            type="search"
            name="searchedUser"
            id="searchedUser"
            value={searchedUser}
            onChange={(e) => setSearchedUser(e.target.value)}
            placeholder="Search user by email..."
            className="border p-2 rounded-md pl-10 w-full focus:outline-none focus:ring-1 focus:ring-secondary text-sm"
            disabled={!ordersWithKnownUsers.length}
          />
        </div>

        {/* Filters */}

        <div className="flex items-center gap-4">
          <label
            htmlFor="deliveryDateFilter"
            className="flex items-center text-sm"
          >
            <span className="mr-2 text-gray-700">Delivery:</span>
            <select
              name="deliveryDateFilter"
              id="deliveryDateFilter"
              className="border p-2 text-sm rounded-md focus:outline-none focus:ring-1 focus:ring-secondary cursor-pointer"
              value={deliveryDateFilter}
              onChange={(e) => setDeliveryDateFilter(e.target.value)}
            >
              <option value="">All Dates</option>
              <option value="today">Today</option>
              <option value="1day">1 Day</option>
              <option value="2day">2 Days</option>
              <option value="3day">3 Days</option>
              <option value="4day">4 Days</option>
              <option value="5day">5 Days</option>
              <option value="6day">6 Days</option>
              <option value="7day">7 Days</option>
            </select>
          </label>

          <div className="flex items-center gap-2">
            <label className="text-sm" htmlFor="selectedDate">
              Date Ordered:
            </label>
            <input
              type="date"
              name="selectedDate"
              id="selectedDate"
              className="border p-2 text-sm rounded-md focus:outline-none focus:ring-1 focus:ring-secondary"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
            />
            <IoClose
              className={selectedDate ? "cursor-pointer" : "hidden"}
              onClick={() => setSelectedDate("")}
            />
          </div>

          <label htmlFor="statusFilter" className="flex items-center text-sm">
            <span className="mr-2 text-gray-700">Status:</span>
            <select
              name="statusFilter"
              id="statusFilter"
              className="border p-2 ml-1 text-sm rounded-md focus:outline-none focus:ring-1 focus:ring-secondary"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="">All Status</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="delivered">Delivered</option>
              <option value="cancel">Canceled</option>{" "}
              <option value="rider">Rider</option>
            </select>
          </label>
        </div>
      </section>
      <section className="w-full bg-white p-3 shadow-md rounded-lg overflow-x-auto">
        <table className="w-[1200px]" style={{ minWidth: "700px" }}>
          <thead className="text-left bg-gray-50 border-b border-gray-200">
            <tr>
              <th className="p-3 text-sm font-semibold text-gray-600">SN</th>
              <th className="p-3 text-sm font-semibold text-gray-600">
                Client
              </th>
              <th className="p-3 text-sm font-semibold text-gray-600">Email</th>
              <th className="p-3 text-sm font-semibold text-gray-600">Card</th>
              {/* Conditional column based on tab */}
              {/* <th className="p-3 text-sm font-semibold text-gray-600">Account Type</th> */}
              <th className="p-3 text-sm font-semibold text-gray-600">
                Details
              </th>
              <th className="p-3 text-sm font-semibold text-gray-600">
                Order Date
              </th>
              <th className="p-3 text-sm font-semibold text-gray-600">
                Status
              </th>
              <th className="p-3 text-sm font-semibold text-gray-600">
                Action
              </th>
              <th className="p-3 text-sm font-semibold text-gray-600">Track</th>
            </tr>
          </thead>
          <tbody className="relative">
            {status === "loading" ? (
              <tr>
                <td colSpan={7} className="text-center p-10">
                  <LoadingSpinner />
                </td>
              </tr>
            ) : currentItems.length > 0 ? (
              currentItems.map((order: any, index: number) => (
                <tr
                  className="border-b border-gray-200 py-2 hover:bg-gray-50 text-sm"
                  key={order._id || index}
                >
                  <td className="text-secondary p-3 font-medium">
                    {index + indexOfFirstPost + 1}
                  </td>
                  <td className="p-3 capitalize">
                    {`${order.userId?.firstName?.toLowerCase() || ""} ${
                      order.userId?.lastName?.toLowerCase() || ""
                    }` || "N/A"}
                  </td>
                  <td className="p-3">
                    <div className="flex gap-2 items-center">
                      {order.userId?.email && (
                        <button
                          type="button"
                          className="text-gray-500 hover:text-secondary transition-colors"
                          title={`Copy ${order.userId.email}`}
                          onClick={() => handleCopyClick(order.userId.email)}
                        >
                          <FaRegCopy className="w-4 h-4" />
                        </button>
                      )}
                      <span>{order.userId?.email || "N/A"}</span>
                    </div>
                  </td>
                  <td className="p-3">
                    {order.cards && order.cards.length > 0 ? (
                      <button
                        type="button"
                        className="text-secondary text-sm font-medium hover:underline"
                        onClick={() => handleViewCard(order.cards)}
                      >
                        View
                      </button>
                    ) : (
                      <span className="text-gray-400 text-xs">No Card</span>
                    )}
                  </td>
                  <td className="p-3">
                    <button
                      type="button"
                      className="p-2 px-3 bg-secondary rounded text-white text-xs hover:bg-secondary-dark transition-colors"
                      onClick={() => handleViewDetails(order)}
                    >
                      View details
                    </button>
                  </td>
                  <td className="p-3">
                    {order.createdAt ? (
                      <span className="text-gray-500">
                        {formatDate(order.createdAt)}
                      </span>
                    ) : (
                      "N/A"
                    )}
                  </td>
                  <td className="p-3">
                    <span
                      className={`px-3 py-1 w-fit rounded-full text-xs font-medium capitalize ${
                        order.status === "approved"
                          ? "bg-green-100 text-green-700"
                          : order.status === "pending"
                          ? "bg-yellow-100 text-yellow-700"
                          : order.status === "delivered"
                          ? "bg-blue-100 text-blue-700"
                          : order.status === "cancel"
                          ? "bg-red-100 text-red-700"
                          : order.status === "rider"
                          ? "bg-purple-100 text-purple-700"
                          : "bg-gray-100 text-gray-700"
                      }`}
                    >
                      {order.status === "cancel" ? "Canceled" : order.status}
                    </span>
                  </td>
                  <td className="p-3">
                    <button
                      type="button"
                      className={`p-2 px-3 rounded text-xs ${
                        order.status !== "pending"
                          ? "bg-gray-200 text-gray-500 cursor-not-allowed"
                          : "text-red-600 border border-red-500 hover:bg-red-50 transition-colors"
                      }`}
                      onClick={() =>
                        setViewCancelOrder((prev) => ({
                          details: order,
                          status: !prev.status,
                          btn: false,
                        }))
                      }
                      disabled={order.status !== "pending"}
                    >
                      {order.status === "cancel" ? "Canceled" : "Cancel Order"}
                    </button>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button
                      onClick={() => handleTrackNow(order)}
                      className="text-green-600 hover:text-green-900"
                    >
                      Track Now
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="text-center text-gray-500 p-6">
                  {searchedUser
                    ? "User email not found in this section."
                    : "No orders match the current filters."}
                </td>
              </tr>
            )}
          </tbody>
        </table>
        {finalFilteredOrders.length > itemsPerPage && (
          <section className="p-3 my-5 border-t border-gray-200">
            <Pagination
              length={finalFilteredOrders.length}
              itemsPerPage={itemsPerPage}
              handlePagination={handlePagination}
              currentPage={currentPage}
              prevPage={prevPage}
              nextPage={nextPage}
            />
          </section>
        )}
      </section>

      {/* --- Cancel Order Modal --- */}
      {viewCancelOrder.status && (
        <div className="fixed z-50 inset-0 bg-black bg-opacity-60 flex items-center justify-center p-4">
          <div className="bg-white p-6 rounded-lg shadow-xl w-full max-w-md mx-auto">
            <h3 className="text-lg font-semibold mb-2 text-gray-800">
              Cancel Order Confirmation
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              Please provide a reason for canceling this order:
            </p>
            <textarea
              name="reason"
              id="reason"
              className="w-full border border-gray-300 p-2 text-sm rounded-md focus:outline-none focus:ring-1 focus:ring-secondary"
              placeholder="Enter reason..."
              rows={3}
              value={cancelReason}
              onChange={(e) => setCancelReason(e.target.value)}
            ></textarea>
            <div className="flex justify-end gap-3 text-sm mt-5">
              <button
                type="button"
                className="py-2 px-4 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors"
                onClick={() => {
                  setCancelReason("");
                  setViewCancelOrder({
                    details: {},
                    btn: false,
                    status: false,
                  });
                }}
              >
                Back
              </button>
              <button
                type="button"
                className={`py-2 px-4 w-28 flex justify-center items-center rounded-md text-white transition-colors ${
                  cancelReason.trim().length === 0
                    ? "bg-red-300 cursor-not-allowed"
                    : "bg-red-600 hover:bg-red-700"
                }`}
                onClick={() =>
                  handleCancelOrder((viewCancelOrder.details as any)?._id)
                }
                disabled={
                  cancelReason.trim().length === 0 || viewCancelOrder.btn
                }
              >
                {viewCancelOrder.btn ? <Preloader /> : "Proceed"}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* --- Order Details Modal --- */}
      {viewDetails && (
        <OrderDetails
          accountType={activeTab}
          orderDetails={orderDetails}
          setViewDetails={setViewDetails}
          pickupDetails={pickupDetails}
          setPickupDetails={setPickupDetails}
          handleApproveOrder={handleApproveOrder}
          handleInitialApprove={handleInitialApprove}
          error={error}
          setError={setError}
          isLoading={isLoading}
        />
      )}

      {/* --- View Card Modal --- */}
      {showCard && (
        <ViewCard
          closeModal={() => setShowCard(false)}
          cardDetails={cardDetails}
        />
      )}

      {/* --- Track Order Modal --- */}
      {showModal && (
        <DeliverySummaryModal
          selectedOrder={selectedOrder}
          onClose={() => setShowModal(false)}
        />
      )}
    </main>
  );
};
