import { useEffect, useState } from "react";
import { formatNumber } from "../../redux/thunk";
import { IoSearchOutline } from "react-icons/io5";
import LoadingSpinner from "../../components/elements/LoadingSpinner";
import { useNavigate } from "react-router-dom";
import Pagination from "../../components/Pagination";
import AddReferralModal from "../../components/modals/AddReferralModal";
import axios from "axios";
import { useAppSelector } from "../../redux/hooks";
import { RootState } from "../../redux/store";
import { IoIosClose } from "react-icons/io";

const SalesReferral = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [searchedUser, setSearchedUser] = useState("");
  const [searchResults, setSearchResults] = useState<any>([]);
  const [filter, setFilter] = useState("");
  const [totalComplete, setTotalComplete] = useState(0);
  const [totalPending, setTotalPending] = useState(0);
  const [totalReferred, setTotalReferred] = useState(0);
  const [showAddReferralModal, setShowAddReferralModal] = useState(false);
  const [referralData, setReferralData] = useState<any[]>([]);
  const { token } = useAppSelector((store: RootState) => store.auth);
  const navigate = useNavigate();

  const getAllReferrers = async () => {
    setIsLoading(true);
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/getAllReferralsList?page=${currentPage}&limit=10`,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      setReferralData(res.data.data);
    } catch (error) {
      console.error("Error fetching referral data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    getAllReferrers();
  }, []);

  const [itemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const indexOfLastPost = currentPage * itemsPerPage;
  const indexOfFirstPost = indexOfLastPost - itemsPerPage;
  const currentItems = referralData.slice(indexOfFirstPost, indexOfLastPost);

  // Pagination
  const handlePagination = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const prevPage = () => {
    if (currentPage !== 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const nextPage = () => {
    if (currentPage !== Math.ceil(referralData.length / itemsPerPage)) {
      setCurrentPage(currentPage + 1);
    }
  };

  const searchForUsersWithEmail = (value: string) => {
    if (filter) {
      setFilter("");
    }
    setSearchedUser(value);
    if (value.length > 0) {
      const searchResult =
        referralData?.filter((user: any) =>
          user?.userId?.email?.toLowerCase().includes(value.toLowerCase())
        ) || [];
      setSearchResults(searchResult);
    } else {
      setSearchResults([]);
    }
  };

  useEffect(() => {
    let totalCount = 0;
    let completeCount = 0;
    let pendingCount = 0;

    if (referralData && referralData.length > 0) {
      referralData.forEach((item: any) => {
        totalCount += item.totalReferredCustomers;
        completeCount += item.totalReferredWithOrders;
        pendingCount += item.totalReferredWithoutOrders;
      });
    }

    setTotalReferred(totalCount);
    setTotalComplete(completeCount);
    setTotalPending(pendingCount);
  }, [referralData]);

  const data =
    searchedUser && searchedUser.length > 0 ? searchResults : currentItems;

  return (
    <main>
      <section className="flex my-5 flex-wrap gap-8">
        <div
          className={`bg-white p-3 rounded-md shadow-md w-48 ${
            isLoading && "animate-pulse "
          }`}
        >
          <p className="font-medium pb-2 text-gray-700 caption-bottom">
            {totalReferred ? formatNumber(totalReferred) : 0}
          </p>
          <h3 className="font-semibold text-sm text-[#8F7803]">
            Total Referrals
          </h3>
        </div>
        <div
          className={`bg-white p-3 rounded-md shadow-md w-48 ${
            isLoading && "animate-pulse "
          }`}
        >
          <p className="font-medium pb-2 text-gray-700 caption-bottom">
            {totalComplete ? formatNumber(totalComplete) : 0}
          </p>
          <h3 className="font-semibold text-sm text-secondary">
            Complete Referrals
          </h3>
        </div>
        <div
          className={`bg-white p-3 rounded-md shadow-md w-48 ${
            isLoading && "animate-pulse "
          }`}
        >
          <p className="font-medium pb-2 text-gray-700 caption-bottom">
            {totalPending ? formatNumber(totalPending) : 0}
          </p>
          <h3 className="font-semibold text-sm text-[#F59040]">
            Pending Referrals
          </h3>
        </div>
        <button
          className={`bg-secondary p-3 rounded-md shadow-md w-48 ${
            isLoading && "animate-pulse "
          }`}
          onClick={() => setShowAddReferralModal(true)}
        >
          <h3 className="font-semibold inline-flex gap-3 text-sm text-white">
            Add Sales User
          </h3>
        </button>
      </section>

      <section className="my-10">
        <h3 className="font-bold text-lg">Sales Referral</h3>
        <section className="flex justify-between items-center pb-4">
          <h3 className="text-secondary text-sm">Activities</h3>
          <div className="flex items-center gap-5">
            <div className="relative">
              <IoSearchOutline className="w-5 h-5 absolute top-[0.5rem] left-1 text-gray-300 text-sm" />
              <input
                type="search"
                name="search"
                id="search"
                value={searchedUser}
                onChange={(e) => searchForUsersWithEmail(e.target.value.trim())}
                className="p-2 indent-5 text-xs bg-[#F9FBFF]"
                placeholder="Search by user email"
              />
            </div>
            <label
              htmlFor="filter-date"
              className="text-xs px-2 bg-[#F9FBFF] flex items-center"
            >
              Sort by:
              <input
                type="date"
                name="filter-date"
                id="filter-date"
                className="py-2 font-semibold mx-2 text-xs bg-[#F9FBFF] cursor-pointer"
                value={filter}
                onChange={(e) => {
                  setFilter(e.target.value);
                  const filtered = referralData.filter((item: any) => {
                    const itemDate = new Date(item.addedDate)
                      .toISOString()
                      .split("T")[0];
                    const filterDate = e.target.value;
                    return itemDate === filterDate;
                  });
                  setSearchResults(filtered);
                  setSearchedUser(" ");
                }}
              />
              <button
                type="button"
                onClick={() => {
                  setFilter("");
                  setSearchedUser("");
                }}
                className={filter ? "block" : "hidden"}
                title="clear calendar"
              >
                <IoIosClose className="w-6 h-6" />
              </button>
            </label>
          </div>
        </section>
        <table className="w-full">
          <thead>
            <tr className="text-[#B5B7C0] text-left text-sm border-b">
              <th className="p-3">SN</th>
              <th className="p-3">Full name</th>
              <th className="p-3">Email Address</th>
              <th className="p-3">Referral Code</th>
              <th className="p-3">Total Amount Earned</th>
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              <tr>
                <td colSpan={5} className="text-center p-3">
                  <LoadingSpinner />
                </td>
              </tr>
            ) : data && data.length > 0 ? (
              data.map((item: any, index: number) => (
                <tr
                  key={index}
                  className={`${
                    item.totalReferredCustomers &&
                    item.totalReferredCustomers > 0
                      ? "cursor-pointer hover:bg-gray-200 "
                      : "cursor-default hover:bg-gray-50 "
                  }`}
                  onClick={() => {
                    if (item.totalReferredCustomers > 0) {
                      navigate("details", {
                        state: {
                          referralId: item._id,
                        },
                      });
                    }
                  }}
                >
                  <td className="p-3 text-sm border-b">
                    {String(index + 1).padStart(2, "0")}
                  </td>
                  <td
                    className={`p-3 text-sm border-b ${
                      item.totalReferredCustomers > 0 ? "underline" : ""
                    }`}
                  >
                    {`${item?.userId?.firstName || "---"} ${
                      item?.userId?.lastName || "---"
                    }`}
                  </td>
                  <td
                    className={`p-3 text-sm border-b ${
                      item.totalReferredCustomers > 0 ? "underline" : ""
                    }`}
                  >
                    {item?.userId?.email || "---"}
                  </td>
                  <td
                    className={`p-3 text-sm border-b ${
                      item.totalReferredCustomers > 0 ? "underline" : ""
                    }`}
                  >
                    {item.userId?.referralCode || "---"}
                  </td>
                  <td className="p-3 text-sm border-b text-secondary">
                    ₦{item.userId?.referralBonusBalance?.toFixed(2) || "0.00"}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={5} className="text-center p-3 text-secondary">
                  {searchedUser.length > 0
                    ? "User not found"
                    : "No customer has made any referrals"}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </section>
      <section className="p-3 my-5">
        <Pagination
          length={data.length}
          itemsPerPage={itemsPerPage}
          handlePagination={handlePagination}
          currentPage={currentPage}
          prevPage={prevPage}
          nextPage={nextPage}
        />
      </section>
      {showAddReferralModal && (
        <AddReferralModal
          closeModal={() => setShowAddReferralModal(false)}
          reloadReferralPage={getAllReferrers}
        />
      )}
    </main>
  );
};

export default SalesReferral;
