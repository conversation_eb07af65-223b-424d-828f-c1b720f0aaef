import { useEffect, useState } from "react";
import { IoIosArrowRoundBack, IoIosArrowRoundForward } from "react-icons/io";
import LoadingSpinner from "../../components/elements/LoadingSpinner";
import { fetchStateUsersWithOrders } from "../../redux/thunk";

interface OrderUser {
  id: string;
  firstName: string;
  lastName: string;
  middleName: string;
  email: string;
  categoryType: string;
  orderCount: number;
}

const StateUsersWithOrders = () => {
  const [searchedUser, setSearchedUser] = useState("");
  const [orderUsers, setOrderUsers] = useState<OrderUser[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalResults, setTotalResults] = useState(0);
  const itemsPerPage = 10;

  useEffect(() => {
    const fetchData = async () => {
      const result = await fetchStateUsersWithOrders(currentPage, itemsPerPage);
      setOrderUsers(result?.results ?? []);
      setCurrentPage(result.currentPage);
      setTotalPages(result.totalPages);
      setTotalResults(result.totalResults);
    };
    fetchData();
  }, []);

  // Pagination
  const goToPage = async (pageNumber: number) => {
    setSearchedUser("");
    setLoading(true);
    const result = await fetchStateUsersWithOrders(pageNumber, itemsPerPage);
    setOrderUsers(result?.results ?? []);
    setCurrentPage(result.currentPage);
    setTotalPages(result.totalPages);
    setTotalResults(result.totalResults);
    setLoading(false);
  };

  const prevPage = () => {
    if (currentPage > 1) {
      goToPage(currentPage - 1);
    }
  };

  const nextPage = () => {
    if (currentPage < totalPages) {
      goToPage(currentPage + 1);
    }
  };

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages = [];
    const maxPagesToShow = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);

    if (endPage - startPage < maxPagesToShow - 1) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    return pages;
  };

  return (
    <section className="mx-auto p-6 bg-white rounded-lg shadow">
      <header className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">
            State Users with Orders
          </h1>
        </div>
        <div className="mt-4 md:mt-0">
          <input
            type="text"
            placeholder="Search by name or email..."
            value={searchedUser}
            onChange={(e) => setSearchedUser(e.target.value)}
            className="border rounded px-3 py-2 text-sm w-64"
          />
        </div>
      </header>
      <div className="overflow-x-auto rounded-lg border">
        <table className="min-w-full bg-white">
          <thead>
            <tr className="bg-gray-100">
              <th className="py-3 px-4 text-left font-semibold text-gray-700">
                Full Name
              </th>
              <th className="py-3 px-4 text-left font-semibold text-gray-700">
                Email
              </th>
              <th className="py-3 px-4 text-left font-semibold text-gray-700">
                Category
              </th>
              <th className="py-3 px-4 text-left font-semibold text-gray-700">
                Order Count
              </th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={4} className="text-center py-8">
                  <LoadingSpinner />
                </td>
              </tr>
            ) : orderUsers.length > 0 ? (
              orderUsers
                .filter(
                  (user) =>
                    user.firstName
                      .toLowerCase()
                      .includes(searchedUser.toLowerCase()) ||
                    user.lastName
                      .toLowerCase()
                      .includes(searchedUser.toLowerCase()) ||
                    user.email
                      .toLowerCase()
                      .includes(searchedUser.toLowerCase())
                )
                .map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="py-3 px-4">
                      {user.firstName} {user.middleName} {user.lastName}
                    </td>
                    <td className="py-3 px-4">{user.email}</td>
                    <td className="py-3 px-4">{user.categoryType}</td>
                    <td className="py-3 px-4">{user.orderCount}</td>
                  </tr>
                ))
            ) : (
              <tr>
                <td colSpan={4} className="text-center py-8 text-gray-500">
                  No orderUsers found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      <footer className="flex flex-col sm:flex-row items-center justify-between mt-6 gap-4">
        <div className="text-sm text-gray-500">
          <p>
            Showing {orderUsers.length} of {totalResults} results
          </p>
          <p>
            Page {currentPage} of {totalPages}
          </p>
        </div>

        <div className="flex items-center gap-2">
          {/* Previous button */}
          <button
            onClick={prevPage}
            className="bg-gray-100 hover:bg-gray-200 p-2 rounded-lg border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={currentPage === 1}
            aria-label="Previous page"
          >
            <IoIosArrowRoundBack className="w-5 h-5 text-gray-600" />
          </button>

          {/* Page numbers */}
          <div className="flex gap-1">
            {/* First page */}
            {currentPage > 3 && (
              <>
                <button
                  onClick={() => goToPage(1)}
                  className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-100"
                >
                  1
                </button>
                {currentPage > 4 && (
                  <span className="px-3 py-2 text-sm text-gray-500">...</span>
                )}
              </>
            )}

            {/* Page numbers around current page */}
            {getPageNumbers().map((pageNum) => (
              <button
                key={pageNum}
                onClick={() => goToPage(pageNum)}
                className={`px-3 py-2 text-sm border rounded-lg ${
                  pageNum === currentPage
                    ? "bg-blue-500 text-white border-blue-500"
                    : "border-gray-300 hover:bg-gray-100"
                }`}
              >
                {pageNum}
              </button>
            ))}

            {/* Last page */}
            {currentPage < totalPages - 2 && (
              <>
                {currentPage < totalPages - 3 && (
                  <span className="px-3 py-2 text-sm text-gray-500">...</span>
                )}
                <button
                  onClick={() => goToPage(totalPages)}
                  className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-100"
                >
                  {totalPages}
                </button>
              </>
            )}
          </div>

          {/* Next button */}
          <button
            onClick={nextPage}
            className="bg-gray-100 hover:bg-gray-200 p-2 rounded-lg border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={currentPage === totalPages}
            aria-label="Next page"
          >
            <IoIosArrowRoundForward className="w-5 h-5 text-gray-600" />
          </button>
        </div>
      </footer>
    </section>
  );
};

export default StateUsersWithOrders;
