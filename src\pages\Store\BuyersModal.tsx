const BuyersModal = ({ setViewBuyers, data }: any) => {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-lg p-6 relative">
        <h2 className="text-xl font-semibold mb-4">Buyers List</h2>
        <button
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-700"
          onClick={() => setViewBuyers({ modal: false, data: [] })}
          aria-label="Close"
        >
          &times;
        </button>
        <div className="overflow-x-auto pb-5">
          <table className="min-w-full border border-gray-200 rounded-lg">
            <thead>
              <tr className="bg-gray-100">
                <th className="px-4 py-2 text-left font-medium text-gray-700">
                  Name
                </th>
                <th className="px-4 py-2 text-left font-medium text-gray-700">
                  Email
                </th>
                <th className="px-4 py-2 text-left font-medium text-gray-700">
                  Quantity
                </th>
                <th className="px-4 py-2 text-left font-medium text-gray-700">
                  Date
                </th>
              </tr>
            </thead>
            <tbody>
              <ul className="space-y-2">
                {data?.buyers?.map((buyer: any, index: number) => (
                  <tr className="border-t" key={index}>
                    <td className="px-4 py-2 text-nowrap">
                      {buyer.name || "--"}
                    </td>
                    <td className="px-4 py-2">{buyer.email || "--"}</td>
                    <td className="px-4 py-2">{buyer.quantity || "--"}</td>
                    <td className="px-4 py-2 text-nowrap">
                      {buyer.date || "--"}
                    </td>
                  </tr>
                ))}
              </ul>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default BuyersModal;
