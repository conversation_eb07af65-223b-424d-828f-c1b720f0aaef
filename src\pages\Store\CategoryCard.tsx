const CategoryCard = ({ category, handleCategoryClick }: any) => {
  return (
    <button
      className="border m-3 p-3 rounded-lg w-56 cursor-pointer hover:shadow-lg transition-shadow"
      onClick={() => handleCategoryClick(category._id, category.type)}
    >
      <p className="text-sm my-2 text-left font-semibold text-secondary">
        {category.type.toUpperCase()}
      </p>
      <div className="w-full h-32 rounded-md bg-gray-50 flex items-center justify-center">
        <img
          src={category.image}
          className="w-full h-full rounded-md object-contain"
          alt={category.type}
        />
      </div>
    </button>
  );
};

export default CategoryCard;
