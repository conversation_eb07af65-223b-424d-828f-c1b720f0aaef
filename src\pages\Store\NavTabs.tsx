const NavTabs = ({
  activeTab,
  setActiveTab,
  products,
  categories,
  adminLevel,
}: any) => {
  return (
    <nav className="flex gap-2">
      <button
        className={`px-3 py-2 rounded-t font-semibold transition-colors ${
          activeTab === "product"
            ? "border-b-4 border-secondary text-secondary"
            : "text-gray-600"
        }`}
        onClick={() => setActiveTab("product")}
        type="button"
      >
        Products ({products ? products?.length : null})
      </button>
      <button
        className={`px-3 py-2 rounded-t font-semibold transition-colors ${
          activeTab === "category"
            ? "border-b-4 border-secondary text-secondary"
            : "text-gray-600"
        }`}
        onClick={() => setActiveTab("category")}
        type="button"
      >
        Categories ({categories ? categories.length : null})
      </button>
      {adminLevel === "superadmin" && (
        <button
          className={`px-3 py-2 rounded-t font-semibold transition-colors ${
            activeTab === "service"
              ? "border-b-4 border-secondary text-secondary"
              : "text-gray-600"
          }`}
          onClick={() => setActiveTab("service")}
          type="button"
        >
          Service & Delivery Fee
        </button>
      )}
    </nav>
  );
};

export default NavTabs;
