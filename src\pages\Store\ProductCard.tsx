const ProductCard = ({ product, handleProductClick }: any) => {
  return (
    <button
      className="group border border-gray-200 shadow-md hover:shadow-lg transition-shadow duration-200 m-3 rounded-xl w-56 flex flex-col justify-between cursor-pointer bg-white overflow-hidden"
      onClick={() => handleProductClick(product._id)}
      aria-label={`View details for ${product.name}`}
    >
      <div className="p-4 flex flex-col gap-2">
        <p className="text-base text-left font-semibold text-gray-800 truncate">
          {product.name}
        </p>
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-500">{product.measurement}</span>
          <span className="text-xs text-secondary">
            Amount Left - {product.quantity}
          </span>
        </div>
      </div>
      <div className="w-full bg-gray-50 h-36 flex items-center justify-center border-y relative">
        {(!product.quantity || product.quantity <= 0) && (
          <p
            className="text-red-500 font-bold absolute text-3xl bg-white bg-opacity-80 px-2 rounded"
            aria-label="Sold Out"
            role="status"
          >
            SOLD OUT
          </p>
        )}
        <img
          src={product.image || product.images?.[0]}
          className={`w-28 h-28 object-contain transition-transform duration-200 ${
            product.quantity && product.quantity > 0
              ? "group-hover:scale-105"
              : ""
          }`}
          alt={product.name || "Product image"}
        />
      </div>
      <div className="flex items-center justify-between px-4 py-3 bg-gray-100">
        <span className="text-sm text-gray-600 capitalize">
          {product.location}
        </span>
        <span className="font-bold text-secondary text-lg">
          ₦{product.price?.toLocaleString()}
        </span>
      </div>
    </button>
  );
};

export default ProductCard;
