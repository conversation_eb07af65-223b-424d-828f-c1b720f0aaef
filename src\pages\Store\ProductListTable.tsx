import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BsTrash2 } from "react-icons/bs";
import { FiEye } from "react-icons/fi";

const ProductListTable = ({
  currentItems,
  setViewBuyers,
  handleProductClick,
  handleDeleteProduct,
}: any) => {
  const getAttributeClass = (attribtes: string) => {
    switch (attribtes) {
      case "topSelling":
        return "text-green-600";
      case "selling":
        return "text-orange-500";
      case "regular":
        return "text-red-500";
      default:
        return "text-gray-500";
    }
  };

  return (
    <table className="min-w-full divide-y divide-gray-200">
      <thead className="bg-gray-50">
        <tr>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Image
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Product Name
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Category
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Price
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Piece
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Buyers
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Attributes
          </th>
          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Action
          </th>
        </tr>
      </thead>
      <tbody className="bg-white divide-y divide-gray-200">
        {currentItems && currentItems.length > 0 ? (
          currentItems.map((product: any) => (
            <tr key={product._id}>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="relative flex-shrink-0 h-12 w-12">
                  <img
                    className="h-12 w-12 rounded-md object-cover"
                    src={product.image || "/placeholder.svg?height=48&width=48"}
                    alt={product.name}
                  />
                  {product.quantity === 0 && (
                    <span className="absolute inset-0 flex items-center justify-center bg-black/60 rounded-md">
                      <span className="text-xs font-bold text-white bg-red-600 px-2 py-0.5 rounded">
                        Sold Out
                      </span>
                    </span>
                  )}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm font-medium text-gray-900">
                  {product.name}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-500">{product.type}</div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900">
                  ₦{product.price?.toLocaleString()}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-500">{product.quantity}</div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <button
                  className={`inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                    product.metadata.buyers.length === 0
                      ? "bg-gray-100 text-gray-400 cursor-default border border-gray-200"
                      : "bg-gradient-to-r from-green-600 to-secondary text-white"
                  }`}
                  disabled={product.metadata.buyers.length === 0}
                  onClick={() =>
                    setViewBuyers({
                      modal: true,
                      data: product.metadata || [],
                    })
                  }
                >
                  <FiEye className="w-4 h-4" /> View
                  {product.metadata.buyers.length > 0 && (
                    <span className="ml-1 px-2 py-0.5 text-xs bg-white/20 rounded-full">
                      {product.metadata.buyers.length}
                    </span>
                  )}
                </button>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div
                  className={`text-sm ${getAttributeClass(product.attribtes)}`}
                >
                  {product.attribtes || "--"}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleProductClick(product._id)}
                    className="text-gray-600 hover:text-gray-900 p-1 rounded-md hover:bg-gray-100"
                  >
                    <BsPencil className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => handleDeleteProduct(product._id)}
                    className="text-red-600 p-1 rounded-md disabled:text-red-300"
                    disabled
                  >
                    <BsTrash2 className="w-5 h-5" />
                  </button>
                </div>
              </td>
            </tr>
          ))
        ) : (
          <tr>
            <td colSpan={7} className="text-center text-secondary py-10">
              No products found
            </td>
          </tr>
        )}
      </tbody>
    </table>
  );
};

export default ProductListTable;
