import { useEffect, useState, useCallback } from "react";
import { useAppDispatch, useAppSelector } from "../../redux/hooks";
import {
  createProductItem,
  createProductType,
  deleteProduct,
  deleteProductType,
  getAllProductType,
  getAllProducts,
  getFees,
  getProduct,
  updateSingleProduct,
} from "../../redux/thunk";
import toast from "react-hot-toast";
import { FaUpload } from "react-icons/fa";
import LoadingSpinner from "../../components/elements/LoadingSpinner";
import ServiceModal from "../../components/modals/ServiceModal";
import { Preloader } from "../../components/elements/Preloader";
import Pagination from "../../components/Pagination";
import axios from "axios";
import { IoFilterOutline } from "react-icons/io5";
import { products } from "../../types/types";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CiSearch } from "react-icons/ci";
import { BsGrid } from "react-icons/bs";
import BuyersModal from "./BuyersModal";
import ProductCard from "./ProductCard";
import ProductListTable from "./ProductListTable";
import CategoryCard from "./CategoryCard";
import NavTabs from "./NavTabs";

export const Store = () => {
  const adminLevel = sessionStorage.getItem("adminLevel");
  const [activeTab, setActiveTab] = useState("product");
  const [showAddProduct, setShowAddProduct] = useState(false);
  const [showUpdateProduct, setShowUpdateProduct] = useState(false);
  const [showAddCategory, setShowAddCategory] = useState(false);
  const [showUpdateCategory, setShowUpdateCategory] = useState(false);
  const [showServiceModal, setShowServiceModal] = useState(false);
  const [searchProducts, setSearchProducts] = useState("");
  const [preview, setPreview] = useState("");
  const [searchResult, setSearchResult] = useState<any[]>([]);
  const [errors, setErrors] = useState<any>({});
  const [filter, setFilter] = useState("");
  const [sortData, setSortData] = useState<products[]>([]);
  const [filterIndicator, setFilterIndicator] = useState(false);
  const [searchIndicator, setSearchIndicator] = useState(false);
  const [feesDetails, setFeesDetails] = useState<any>({});
  const [viewMode, setViewMode] = useState("grid");
  const [filterCategory, setFilterCategory] = useState("");
  const [viewBuyers, setViewBuyers] = useState<{
    modal: boolean;
    data: any[];
  }>({
    modal: false,
    data: [],
  });
  const [isLoading, setIsLoading] = useState({
    updateProd: false,
    deleteProd: false,
    createProd: false,
    createCat: false,
    deleteCat: false,
  });
  const [singleProduct, setSingleProduct] = useState<any>({
    name: "",
    type: "",
    measurement: "",
    quantity: 0,
    lagosPrice: "",
    oyoPrice: "",
    description: "",
    image: "",
  });
  const [categoryDetails, setCategoryDetails] = useState<any>({
    id: "",
    name: "",
  });
  const [addCategory, setAddCategory] = useState<any>({
    name: "",
    img: "",
  });
  const [addProduct, setAddProduct] = useState<any>({
    name: "",
    measurement: "",
    image: "",
    type: "",
    price: [],
    location: [],
    quantity: "",
    description: "",
    lagosSelected: false,
    oyoSelected: false,
    lagosPrice: "",
    oyoPrice: "",
  });

  const dispatch = useAppDispatch();
  const { products, categories, status } = useAppSelector(
    (store) => store.products
  );
  const { fees } = useAppSelector((store) => store.fees);

  useEffect(() => {
    dispatch(getAllProducts());
    dispatch(getFees());
    dispatch(getAllProductType());
  }, [dispatch]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSingleProduct({
        ...singleProduct,
        image: file,
      });
      const reader = new FileReader();
      reader.onloadend = () => {
        if (typeof reader.result === "string") {
          setPreview(reader.result);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleIconClick = () => {
    const fileInput = document.getElementById(
      "fileInput"
    ) as HTMLInputElement | null;
    fileInput?.click();
  };

  const getProductDetails = async (id: any) => {
    try {
      const productResponse = await dispatch(getProduct(id));
      const productData = productResponse.payload;
      setSingleProduct(productData);
      setShowUpdateProduct((prev) => !prev);
    } catch (error: any) {
      console.error("Error fetching product details:", error);
      toast.error(error.message || "An error occured!");
    }
  };

  const handleCategoryClick = (catId: string, name: string) => {
    setShowUpdateCategory((prev) => !prev);
    setCategoryDetails({
      id: catId,
      name,
    });
  };

  const handleProductClick = (id: any) => {
    getProductDetails(id);
  };

  const handleInputChange = (e: any) => {
    const { name, value } = e.target;
    setSingleProduct({
      ...singleProduct,
      [name]: value,
    });
  };

  const handleUpdateProduct = async (productId: any) => {
    setIsLoading((prev) => ({ ...prev, updateProd: true }));
    try {
      const formData = new FormData();
      formData.append("name", singleProduct.name);
      formData.append("quantity", singleProduct.quantity);
      formData.append("description", singleProduct.description);
      formData.append("measurement", singleProduct.measurement);
      formData.append("price", singleProduct.price);
      formData.append("image", singleProduct.image);
      const response = await dispatch(
        updateSingleProduct({ data: formData, id: productId })
      );

      if (response.payload) {
        toast.success(response.payload.message);
        dispatch(getAllProducts());
        setSearchProducts("");
        setShowUpdateProduct((prev) => !prev);
      } else {
        toast.error("Failed to update product");
      }
    } catch (error: any) {
      toast.error(error.message || "An error occurred!");
    } finally {
      setIsLoading((prev) => ({ ...prev, updateProd: false }));
    }
  };

  const handleDeleteProduct = (productId: any) => {
    setIsLoading((prev) => ({ ...prev, deleteProd: true }));
    try {
      dispatch(deleteProduct(productId)).then((response) => {
        setFilter("");
        dispatch(getAllProducts());

        toast.success(
          response.payload.message || "Product deleted successfully!"
        );
        setShowUpdateProduct((prev) => !prev);
      });
    } catch (error: any) {
      toast.error(error.message || "Deletion failed");
    } finally {
      setIsLoading((prev) => ({ ...prev, deleteProd: false }));
    }
  };

  const handleDeleteCategory = (categoryId: string) => {
    setIsLoading((prev) => ({ ...prev, deleteCat: true }));
    try {
      dispatch(deleteProductType(categoryId)).then((response) => {
        dispatch(getAllProductType());
        toast.success(response.payload.message);
        setShowUpdateCategory((prev) => !prev);
      });
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setIsLoading((prev) => ({ ...prev, deleteCat: false }));
    }
  };

  const catFileChange = (e: any) => {
    if (e.target.files && e.target.files.length > 0) {
      setAddCategory({
        ...addCategory,
        img: e.target.files[0],
      });
    }
  };

  const productFileChange = (e: any, name: any) => {
    if (name === "image" && e.target.files && e.target.files.length > 0) {
      setAddProduct({
        ...addProduct,
        image: e.target.files[0],
      });
    } else if (name === "lagosPrice" || name === "oyoPrice") {
      const value = e.target.value.trim() === "" ? "" : e.target.value;
      setAddProduct({
        ...addProduct,
        [name]: value,
      });
    } else {
      setAddProduct({
        ...addProduct,
        [name]: e.target.value,
      });
    }
  };

  const createCategory = async () => {
    if (addCategoryFormValidation()) {
      toast.error("Please complete the form to add category");
      return;
    }

    const formData = new FormData();
    formData.append("type", addCategory.name);
    formData.append("image", addCategory.img);
    setIsLoading((prev) => ({ ...prev, createCat: true }));

    try {
      dispatch(createProductType(formData)).then(() => {
        dispatch(getAllProductType()).then(() => {
          toast.success("New Category Created!");
          setShowAddCategory((prev) => !prev);
        });
      });
    } catch (error: any) {
      toast.error(error.message || "Category not created!");
    } finally {
      setIsLoading((prev) => ({ ...prev, createCat: false }));
    }
  };

  const handleSearch = (value: string) => {
    if (value.length === 0) {
      setSearchResult([]);
      setFilter("");
    }
    setSearchProducts(value);
    if (value.length > 0) {
      const productData = sortData.length > 0 ? sortData : products;
      const searchedItems = productData.filter((item: any) =>
        item.name.toLowerCase().includes(value.toLowerCase())
      );
      setSearchResult(searchedItems);
    }
  };

  const GetImageURL = async (file: File) => {
    const formData = new FormData();
    formData.append("file", file);
    try {
      const res = await axios.post(
        `${process.env.REACT_APP_API_URL}/upload/file`,
        formData
      );
      return res.data.url;
    } catch (error) {
      console.error("Error uploading image:", error);
    }
  };

  const createProduct = async () => {
    if (addProductFormValidation()) {
      toast.error("Please complete the form to add product");
      return;
    } else {
      setIsLoading((prev) => ({ ...prev, createProd: true }));
      try {
        const imageUrl = await GetImageURL(addProduct.image);

        const prices: number[] = [];
        const locations: string[] = [];

        if (addProduct.lagosSelected && addProduct.lagosPrice) {
          prices.push(Number(addProduct.lagosPrice));
          locations.push("lagos");
        }

        if (addProduct.oyoSelected && addProduct.oyoPrice) {
          prices.push(Number(addProduct.oyoPrice));
          locations.push("oyo");
        }

        const payload = {
          name: addProduct.name,
          measurement: addProduct.measurement,
          type: addProduct.type,
          price: prices,
          location: locations,
          quantity: Number(addProduct.quantity),
          image: imageUrl,
          description: addProduct.description,
        };

        const response = await dispatch(createProductItem(payload));

        if ("error" in response) {
          toast.error("Failed to create product");
          return;
        }

        setFilter("");
        await dispatch(getAllProducts());
        toast.success("New Product(s) Created!");
        setShowAddProduct((prev) => !prev);

        setAddProduct({
          name: "",
          measurement: "",
          image: "",
          type: "",
          price: [],
          location: [],
          quantity: "",
          description: "",
          lagosSelected: false,
          oyoSelected: false,
          lagosPrice: "",
          oyoPrice: "",
        });
      } catch (error: any) {
        toast.error(error.message || "Product not created!");
      } finally {
        setIsLoading((prev) => ({ ...prev, createProd: false }));
      }
    }
  };

  const addProductFormValidation = () => {
    let hasErrors = false;
    const newErrors: any = {};

    if (!addProduct.name) {
      newErrors.productName = "Please enter product name";
      hasErrors = true;
    }
    if (!addProduct.measurement) {
      newErrors.productMeasurement = "Please enter product measurement";
      hasErrors = true;
    }
    if (!addProduct.image) {
      newErrors.productImage = "Please attach product image";
      hasErrors = true;
    }
    if (!addProduct.type) {
      newErrors.productType = "Please select a product category";
      hasErrors = true;
    }

    const hasLagosPrice = addProduct.lagosSelected && addProduct.lagosPrice;
    const hasOyoPrice = addProduct.oyoSelected && addProduct.oyoPrice;

    if (!hasLagosPrice && !hasOyoPrice) {
      newErrors.productPrice =
        "Please select at least one location and enter its price";
      hasErrors = true;
    }

    if (addProduct.lagosSelected && !addProduct.lagosPrice) {
      newErrors.lagosPrice = "Please enter price for Lagos";
      hasErrors = true;
    }

    if (addProduct.oyoSelected && !addProduct.oyoPrice) {
      newErrors.oyoPrice = "Please enter price for Oyo";
      hasErrors = true;
    }

    if (!addProduct.quantity) {
      newErrors.productQuantity = "Please enter product quantity";
      hasErrors = true;
    }
    if (!addProduct.description) {
      newErrors.productDescription = "Please enter product description";
      hasErrors = true;
    }

    setErrors(newErrors);
    return hasErrors;
  };
  const addCategoryFormValidation = () => {
    let hasErrors = false;
    const newErrors: any = {};

    if (!addCategory.name) {
      newErrors.categoryName = "Please enter category name";
      hasErrors = true;
    }
    if (!addCategory.img) {
      newErrors.categoryImage = "Please attach category image";
      hasErrors = true;
    }

    setErrors(newErrors);
    return hasErrors;
  };

  const [itemsPerPage] = useState(12);
  const [currentPage, setCurrentPage] = useState(1);
  const indexOfLastPost = currentPage * itemsPerPage;
  const indexOfFirstPost = indexOfLastPost - itemsPerPage;

  const getData = () => {
    // Both search and sort data exist
    if (searchProducts.length > 0 && sortData.length > 0) {
      // if filter was last used, filterIndicator is true
      if (filterIndicator) {
        return sortData;
        // if Search bar was last used, filterIndicator is true
      } else if (searchIndicator) {
        return searchResult;
      }
    }
    // Only sort data exists
    else if (sortData.length > 0) {
      return sortData;
    }
    // Only search data exists
    else if (searchProducts.length > 0) {
      return searchResult;
    }
    // Default to original products list
    else {
      return products;
    }
  };

  const data = getData();

  const currentItems = data?.slice(indexOfFirstPost, indexOfLastPost);

  const filterProductsByLocationAndCategory = useCallback(() => {
    if (!filter && !filterCategory) {
      return [];
    }

    const productData = searchResult.length > 0 ? searchResult : products;
    const filtered = productData.filter((product: any) => {
      const matchesLocation =
        !filter ||
        (filter === "Lagos State" && product.location === "lagos") ||
        (filter === "Oyo State" && product.location === "oyo");

      const matchesCategory =
        !filterCategory || filterCategory === product.type;

      return matchesLocation && matchesCategory;
    });

    return filtered.length > 0 ? filtered : [];
  }, [filter, filterCategory, products, searchResult]);

  const editServiceOrDeliveryFees = (selectedFee: any) => {
    setFeesDetails(selectedFee);
    setShowServiceModal(true);
  };

  useEffect(() => {
    const filteredProducts = filterProductsByLocationAndCategory();
    setSortData(filteredProducts);
  }, [filter, filterCategory, filterProductsByLocationAndCategory]);

  const handlePagination = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const prevPage = () => {
    if (currentPage !== 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const nextPage = () => {
    if (currentPage !== Math.ceil(products.length / itemsPerPage)) {
      setCurrentPage(currentPage + 1);
    }
  };

  return (
    <>
      <section className="w-full bg-[#F6F4F8]">
        <div className="bg-white w-full rounded-sm p-5">
          <h2 className="font-bold text-xl">Actions</h2>
          <div className="flex gap-3 my-5">
            <button
              className="border rounded-md px-4 py-2 bg-secondary text-white"
              onClick={() => setShowAddProduct(true)}
            >
              Add Product
            </button>
            <button
              className="border rounded-md px-4 py-2 bg-secondary text-white"
              onClick={() => setShowAddCategory(true)}
            >
              Add Category
            </button>
            {adminLevel === "superadmin" && (
              <button
                type="button"
                className="border rounded-md px-4 py-2 bg-secondary text-white"
                onClick={() => setShowServiceModal((prev) => !prev)}
              >
                Set Service and Delivery Fee
              </button>
            )}
          </div>
        </div>
        <section className="bg-white w-full rounded-sm my-5 py-5 px-2">
          <section className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
            <div className="flex flex-wrap items-center gap-4">
              {/* Tabs */}
              <NavTabs
                activeTab={activeTab}
                setActiveTab={setActiveTab}
                products={products}
                categories={categories}
                adminLevel={adminLevel}
              />
            </div>
            {/* Filter */}
            {activeTab === "product" && (
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2 text-sm mt-2 md:mt-0">
                  <IoFilterOutline className="w-5 h-5 text-secondary" />
                  <select
                    name="filter"
                    id="filter"
                    value={filter}
                    onChange={(e) => {
                      setFilterIndicator(true);
                      setSearchIndicator(false);
                      setFilter(e.target.value);
                    }}
                    className="border rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-secondary"
                  >
                    <option value="">Locations</option>
                    <option value="Lagos State">Lagos</option>
                    <option value="Oyo State">Oyo</option>
                  </select>
                </div>
                <div className="flex items-center gap-2 text-sm mt-2 md:mt-0">
                  <select
                    name="filterCategory"
                    id="filterCategory"
                    value={filterCategory}
                    onChange={(e) => {
                      setFilterIndicator(true);
                      setSearchIndicator(false);
                      setFilterCategory(e.target.value);
                    }}
                    className="border rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-secondary"
                  >
                    <option value="">Categories</option>
                    {categories.map((category: any, index: number) => (
                      <option key={index} value={category.type}>
                        {category.type}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            )}
          </section>
          <section>
            <div className="flex justify-between items-center my-6">
              {/* Search */}
              {activeTab === "product" && (
                <div className="ml-4 w-full max-w-md">
                  <div className="relative">
                    <input
                      type="search"
                      name="search"
                      id="search"
                      value={searchProducts}
                      onChange={(e) => {
                        setFilterIndicator(false);
                        setSearchIndicator(true);
                        handleSearch(e.target.value.trim());
                      }}
                      placeholder="Search products..."
                      className="w-full rounded-lg border border-gray-300 bg-white py-2 pl-10 pr-4 text-sm text-gray-800 shadow-sm focus:border-primary focus:ring-2 focus:ring-secondary outline-none transition"
                    />
                    <CiSearch className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5" />
                  </div>
                </div>
              )}

              {/* View toggle */}
              {activeTab === "product" && (
                <div className="flex">
                  <button
                    className={`px-3 py-1 rounded-l flex items-center gap-2 ${
                      viewMode === "grid"
                        ? "bg-secondary text-white"
                        : "bg-gray-100"
                    }`}
                    onClick={() => setViewMode("grid")}
                    type="button"
                    aria-label="Grid view"
                  >
                    <BsGrid className="w-6 h-6" />
                  </button>
                  <button
                    className={`px-3 py-1 rounded-r flex items-center gap-2 ${
                      viewMode === "list"
                        ? "bg-secondary text-white"
                        : "bg-gray-100"
                    }`}
                    onClick={() => setViewMode("list")}
                    type="button"
                    aria-label="List view"
                  >
                    <CiBoxList className="w-6 h-6" />
                  </button>
                </div>
              )}
            </div>
            {/* Products grid or list view */}
            <section
              className={`${
                (activeTab === "product" && viewMode === "grid") ||
                activeTab === "category"
                  ? "w-full grid lg:grid-cols-4 md:grid-cols-3 sm:grid-cols-2 justify-items-center rounded my-2 gap-5"
                  : ""
              }`}
            >
              {status === "loading" ? (
                <LoadingSpinner />
              ) : activeTab === "product" ? (
                (filter || filterCategory) && sortData.length === 0 ? (
                  <p className="text-center text-secondary my-10">
                    No products found
                  </p>
                ) : viewMode === "grid" ? (
                  currentItems && currentItems.length > 0 ? (
                    currentItems.map((product: any, index: number) => (
                      <ProductCard
                        key={index}
                        product={product}
                        handleProductClick={handleProductClick}
                      />
                    ))
                  ) : (
                    <p className="text-center text-secondary my-10">
                      No products found
                    </p>
                  )
                ) : (
                  // List view
                  <div className="bg-white rounded-md shadow overflow-hidden w-full">
                    <div className="overflow-x-auto">
                      <ProductListTable
                        currentItems={currentItems}
                        handleProductClick={handleProductClick}
                        setViewBuyers={setViewBuyers}
                        handleDeleteProduct={handleDeleteProduct}
                      />
                    </div>
                  </div>
                )
              ) : activeTab === "category" ? (
                categories && categories.length > 0 ? (
                  categories.map((category: any, index: number) => (
                    <CategoryCard
                      key={index}
                      category={category}
                      handleCategoryClick={handleCategoryClick}
                    />
                  ))
                ) : (
                  <p className="text-center text-secondary my-10">
                    No categories found
                  </p>
                )
              ) : (
                <div className="my-6 flex flex-wrap gap-6">
                  {fees && fees?.allfees?.length > 0 ? (
                    fees.allfees.map((fee: any, index: number) => (
                      <div
                        key={index}
                        className="bg-gray-50 border border-gray-200 rounded-lg shadow-sm p-5 min-w-[220px] max-w-xs flex flex-col gap-3"
                      >
                        <div className="flex items-center gap-2">
                          <span className="font-semibold text-secondary">
                            Location:
                          </span>
                          <span className="capitalize">
                            {fee.stateLocation || "--"}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="font-semibold text-secondary">
                            Service Fee:
                          </span>
                          <span>
                            ₦{fee.serviceFee?.toLocaleString() || "--"}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="font-semibold text-secondary">
                            Delivery Fee:
                          </span>
                          <span>
                            ₦{fee.deliveryFee?.toLocaleString() || "--"}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="font-semibold text-secondary">
                            Interest:
                          </span>
                          <span>{fee.interest ?? "--"}%</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="font-semibold text-secondary">
                            Increment:
                          </span>
                          <span>{fee.increment ?? "--"}</span>
                        </div>
                        <button
                          type="button"
                          className="mt-2 px-4 py-1 rounded text-white bg-secondary"
                          onClick={() => editServiceOrDeliveryFees(fee)}
                        >
                          Edit
                        </button>
                      </div>
                    ))
                  ) : (
                    <p className="text-center text-gray-500">
                      No fee information available.
                    </p>
                  )}
                </div>
              )}
            </section>
          </section>
          {activeTab === "product" && (
            <section className="p-3 my-5">
              <Pagination
                length={
                  searchProducts.length > 0
                    ? searchResult.length
                    : sortData.length > 0
                    ? sortData.length
                    : products.length
                }
                itemsPerPage={itemsPerPage}
                handlePagination={handlePagination}
                currentPage={currentPage}
                prevPage={prevPage}
                nextPage={nextPage}
              />
            </section>
          )}
        </section>
        {showServiceModal && (
          <ServiceModal
            setShowServiceModal={setShowServiceModal}
            feesDetails={feesDetails}
            setFeesDetails={setFeesDetails}
          />
        )}

        {showAddProduct && (
          <div className="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-40 text-sm">
            <div className="bg-white p-4 rounded-md" style={{ width: "500px" }}>
              <div className="text-dark p-2 rounded-t-md mb-4">
                <div className="relative w-full">
                  <h2 className="font-bold text-xl">Add Product</h2>
                  <button
                    onClick={() => setShowAddProduct(false)}
                    className="text-dark absolute right-0 top-0"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>

                <form action="" className="text-sm">
                  <div className="flex justify-between items-center my-1">
                    <label htmlFor="name">Product Name:</label>
                    <input
                      className="border p-2 w-2/3"
                      type="text"
                      name="name"
                      id="name"
                      placeholder="Enter product name"
                      onChange={(e) => productFileChange(e, "name")}
                    />
                  </div>
                  {errors.productName && (
                    <span className="text-red-500 text-xs">
                      {errors.productName}
                    </span>
                  )}

                  <div className="flex justify-between items-center my-1">
                    <label htmlFor="type">Category</label>
                    <select
                      name="type"
                      id="type"
                      className="w-2/3 border p-2"
                      value={addProduct.type}
                      onChange={(e) => productFileChange(e, "type")}
                    >
                      <option value="">Select product category</option>
                      {categories?.map((type: any, index: number) => (
                        <option key={index} value={type.type}>
                          {type.type}
                        </option>
                      ))}
                    </select>
                  </div>

                  {errors.productType && (
                    <span className="text-red-500 text-xs">
                      {errors.productType}
                    </span>
                  )}
                  <div className="flex justify-between items-center my-1 text-sm">
                    <label htmlFor="quantity">Quantity:</label>
                    <input
                      className="border p-2 w-2/3"
                      type="number"
                      name="quantity"
                      id="quantity"
                      placeholder="Enter product quantity"
                      onChange={(e) => productFileChange(e, "quantity")}
                    />
                  </div>
                  {errors.productQuantity && (
                    <span className="text-red-500 text-xs">
                      {errors.productQuantity}
                    </span>
                  )}

                  <div className="flex justify-between items-center my-1">
                    <label htmlFor="measurement">Measurement:</label>
                    <input
                      className="border p-2 w-2/3"
                      type="text"
                      name="measurement"
                      id="measurement"
                      placeholder="Enter product measurement eg. 1kg, 1ltr"
                      onChange={(e) => productFileChange(e, "measurement")}
                    />
                  </div>
                  {errors.productMeasurement && (
                    <span className="text-red-500 text-xs">
                      {errors.productMeasurement}
                    </span>
                  )}
                  <section className="flex justify-between items-center my-1 text-sm">
                    <label htmlFor="price">Selling Price:</label>
                    <section className="flex flex-col w-2/3 gap-2">
                      <div className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          id="lagosSelected"
                          checked={addProduct.lagosSelected}
                          onChange={(e) =>
                            setAddProduct({
                              ...addProduct,
                              lagosSelected: e.target.checked,
                            })
                          }
                        />
                        <label htmlFor="lagosSelected">Lagos</label>
                        <input
                          className={`border p-2 w-[8rem] ${
                            addProduct.lagosSelected ? "" : "bg-gray-100"
                          }`}
                          type="number"
                          name="lagosPrice"
                          id="lagosPrice"
                          placeholder="Price (₦)"
                          value={addProduct.lagosPrice}
                          onChange={(e) => productFileChange(e, "lagosPrice")}
                          disabled={!addProduct.lagosSelected}
                        />
                      </div>
                      {errors.lagosPrice && (
                        <span className="text-red-500 text-xs">
                          {errors.lagosPrice}
                        </span>
                      )}
                      <div className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          id="oyoSelected"
                          checked={addProduct.oyoSelected}
                          onChange={(e) =>
                            setAddProduct({
                              ...addProduct,
                              oyoSelected: e.target.checked,
                            })
                          }
                        />
                        <label htmlFor="oyoSelected">Oyo</label>
                        <input
                          className={`border p-2 w-[8rem] ${
                            addProduct.oyoSelected ? "" : "bg-gray-100"
                          }`}
                          type="number"
                          name="oyoPrice"
                          id="oyoPrice"
                          placeholder="Price (₦)"
                          value={addProduct.oyoPrice}
                          onChange={(e) => productFileChange(e, "oyoPrice")}
                          disabled={!addProduct.oyoSelected}
                        />
                      </div>
                      {errors.oyoPrice && (
                        <span className="text-red-500 text-xs">
                          {errors.oyoPrice}
                        </span>
                      )}
                    </section>
                  </section>
                  {errors.productPrice && (
                    <span className="text-red-500 text-xs">
                      {errors.productPrice}
                    </span>
                  )}

                  <div className="flex justify-between items-center my-1 text-sm">
                    <label htmlFor="image">Add Image:</label>
                    <input
                      type="file"
                      name="image"
                      id="image"
                      onChange={(e) => productFileChange(e, "image")}
                    />
                  </div>
                  {errors.productImage && (
                    <span className="text-red-500 text-xs">
                      {errors.productImage}
                    </span>
                  )}

                  <div className="flex justify-between items-start my-1 text-sm">
                    <label htmlFor="description" className="mt-2">
                      Description:
                    </label>
                    <textarea
                      className="border p-2 w-2/3 resize-none"
                      name="description"
                      id="description"
                      rows={3}
                      placeholder="Enter product description"
                      onChange={(e) => productFileChange(e, "description")}
                    ></textarea>
                  </div>
                  {errors.productDescription && (
                    <span className="text-red-500 text-xs">
                      {errors.productDescription}
                    </span>
                  )}
                </form>
                <div>
                  <button
                    type="button"
                    onClick={createProduct}
                    className="px-5 py-2 mr-3 rounded text-white bg-secondary"
                  >
                    {isLoading.createProd ? <Preloader /> : "Add Product"}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
        {showUpdateProduct && (
          <div className="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-40">
            <div className="bg-white p-4 rounded-md" style={{ width: "500px" }}>
              <div className="  text-dark p-2 rounded-t-md mb-4">
                <div className="relative w-full">
                  <h2 className="font-bold text-xl">Update Product</h2>
                  <button
                    onClick={() => setShowUpdateProduct((prev) => !prev)}
                    className="text-dark absolute right-0 top-0"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>

                <form action="">
                  <div className="bg-gray-100 h-24 w-24 shadow relative flex items-center justify-center">
                    <img
                      src={preview || singleProduct.image}
                      alt={singleProduct.name}
                      className="h-24 w-24 object-cover"
                    />
                    <input
                      type="file"
                      id="fileInput"
                      style={{ display: "none" }}
                      onChange={handleImageChange}
                      accept="image/*"
                    />
                    <div
                      className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 cursor-pointer"
                      onClick={handleIconClick}
                    >
                      <FaUpload className="text-white text-xl" />
                    </div>
                  </div>
                  <div className="flex justify-between my-2">
                    <label htmlFor="name">Product Name:</label>
                    <input
                      className="border p-2"
                      type="text"
                      name="name"
                      id="name"
                      value={singleProduct.name}
                      onChange={(e) => handleInputChange(e)}
                    />
                  </div>
                  <div className="border bg-gray-300 flex w-full justify-center p-2">
                    <select name="type" id="type" className="bg-gray-300">
                      <option value={singleProduct.type}>
                        {singleProduct.type}
                      </option>
                    </select>
                  </div>
                  <div className="flex justify-between my-2">
                    <label htmlFor="measurement">Measurement:</label>
                    <input
                      className="border p-2 "
                      type="text"
                      name="measurement"
                      id="measurement"
                      value={singleProduct.measurement}
                      onChange={(e) => handleInputChange(e)}
                    />
                  </div>
                  <div className="flex justify-between my-2">
                    <label htmlFor="location">Location:</label>
                    <input
                      className="border p-2 "
                      type="text"
                      name="location"
                      id="location"
                      value={singleProduct.location}
                      onChange={(e) => handleInputChange(e)}
                    />
                  </div>
                  <section className="flex justify-between items-center my-1">
                    <label htmlFor="price">Selling Price:</label>
                    <input
                      className="border p-2 "
                      type="text"
                      name="price"
                      id="price"
                      placeholder="N0.00"
                      value={singleProduct.price ? singleProduct.price : ""}
                      onChange={(e) => handleInputChange(e)}
                    />
                  </section>
                  {errors.productPrice && (
                    <span className="text-red-500 text-xs">
                      {errors.productPrice}
                    </span>
                  )}

                  <div className="flex justify-between my-2">
                    <label htmlFor="quantity">Quantity:</label>
                    <input
                      className="border p-2"
                      type="number"
                      name="quantity"
                      id="quantity"
                      value={singleProduct.quantity}
                      onChange={(e) => handleInputChange(e)}
                    />
                  </div>
                  <div className="flex justify-between my-2">
                    <label htmlFor="description">Description:</label>
                    <input
                      className="border p-2"
                      type="text"
                      name="description"
                      id="description"
                      value={singleProduct.description}
                      onChange={(e) => handleInputChange(e)}
                    />
                  </div>
                </form>
                <div>
                  <button
                    type="button"
                    onClick={() => handleUpdateProduct(singleProduct._id)}
                    className="px-5 py-2 mr-3 rounded text-white bg-secondary"
                  >
                    {isLoading.updateProd ? <Preloader /> : "Update"}
                  </button>
                  <button
                    type="button"
                    onClick={() => handleDeleteProduct(singleProduct._id)}
                    className="px-5 py-2 mr-3 rounded text-white bg-red-500 disabled:bg-red-200 cursor-not-allowed"
                    disabled
                  >
                    {isLoading.deleteProd ? <Preloader /> : "Delete"}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
        {showAddCategory && (
          <div className="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-40">
            <div className="bg-white p-4 rounded-md" style={{ width: "500px" }}>
              <div className="text-dark p-2 rounded-t-md mb-4">
                <div className="relative w-full">
                  <h2 className="font-bold text-xl">Add Category</h2>
                  <button
                    onClick={() => setShowAddCategory(false)}
                    className="text-dark absolute right-0 top-0"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>

                <div>
                  <div className="flex justify-between my-2">
                    <label htmlFor="name">Category Name:</label>
                    <input
                      className="border p-2 "
                      type="text"
                      name="name"
                      id="name"
                      onChange={(e) =>
                        setAddCategory({ ...addCategory, name: e.target.value })
                      }
                    />
                  </div>
                  {errors.categoryName && (
                    <span className="text-red-500 text-xs">
                      {errors.categoryName}
                    </span>
                  )}

                  <div className="flex justify-between my-2">
                    <label htmlFor="img">Add Image:</label>
                    <input
                      type="file"
                      name="img"
                      id="img"
                      onChange={(e) => catFileChange(e)}
                    />
                  </div>
                  {errors.categoryImage && (
                    <span className="text-red-500 text-xs">
                      {errors.categoryImage}
                    </span>
                  )}
                </div>
                <div>
                  <button
                    type="button"
                    onClick={createCategory}
                    className="px-5 py-2 mr-3 rounded text-white bg-secondary"
                  >
                    {isLoading.createCat ? <Preloader /> : "Submit"}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
        {showUpdateCategory && (
          <div className="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50 z-40">
            <div className="bg-white p-4 rounded-md" style={{ width: "500px" }}>
              <div className="text-dark p-2 rounded-t-md mb-4">
                <div className="relative w-full">
                  <h2 className="font-bold text-xl">Update Category</h2>
                  <button
                    onClick={() => setShowUpdateCategory(false)}
                    className="text-dark absolute right-0 top-0"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>

                <div className="flex justify-between my-2">
                  <label htmlFor="name">Category Name:</label>
                  <input
                    className="border p-2"
                    type="text"
                    name="name"
                    id="name"
                    value={categoryDetails.name}
                    disabled
                  />
                </div>
                <div>
                  <button
                    type="button"
                    onClick={() => setShowUpdateCategory(false)}
                    className="px-5 py-2 mr-3 rounded text-white bg-secondary"
                  >
                    Submit
                  </button>
                  <button
                    type="button"
                    onClick={() => handleDeleteCategory(categoryDetails.id)}
                    className="px-5 py-2 mr-3 rounded text-white bg-red-500 disabled:bg-red-200 cursor-not-allowed"
                    disabled
                  >
                    {isLoading.deleteCat ? <Preloader /> : "Delete"}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
        {viewBuyers.modal && (
          <BuyersModal setViewBuyers={setViewBuyers} data={viewBuyers.data} />
        )}
      </section>
    </>
  );
};
