import { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, FaC<PERSON>, FaSearch } from "react-icons/fa";
import TransactionModal from "../../components/modals/TransactionModal";
import { useAppDispatch, useAppSelector } from "../../redux/hooks";
import { getEventsGroupedByUser } from "../../redux/thunk";
import toast from "react-hot-toast";

const Transactions = () => {
  const dispatch = useAppDispatch();
  const { transactionGroups, status, error } = useAppSelector((state) => state.transactions);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [showModal, setShowModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [userTypeFilter, setUserTypeFilter] = useState("all");

  // Process transaction groups to show one row per user (not per transaction)
  const allTransactions = (transactionGroups || [])
    .map((group, groupIndex) => {
      // Ensure group has required properties
      if (!group || !group.userDetails || !group.transactionDetails || !Array.isArray(group.transactionDetails)) {
        return null;
      }

      return {
        id: `user_${groupIndex + 1}`,
        name: `${group.userDetails.firstName || ''} ${group.userDetails.lastName || ''}`.trim(),
        email: group.userDetails.email || '',
        accountType: group.userDetails.accountType || 'flexible',
        userDetails: group.userDetails,
        transactionDetails: group.transactionDetails,
        walletBalance: group.userDetails.virtualAccount?.walletbalance || 0,
        transactionCount: group.transactionDetails.length
      };
    })
    .filter((item): item is NonNullable<typeof item> => item !== null); // Remove null entries with type guard

  // Filter transactions based on search term (email) and user type
  const filteredTransactions = allTransactions.filter(transaction => {
    const matchesSearch = transaction.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesUserType = userTypeFilter === "all" || transaction.accountType === userTypeFilter;
    return matchesSearch && matchesUserType;
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const totalPages = Math.ceil(filteredTransactions.length / itemsPerPage);

  // Get current page transactions
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentTransactions = filteredTransactions.slice(startIndex, endIndex);

  // Reset to first page when search term or user type filter changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, userTypeFilter]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePrevious = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  // Clipboard copy function
  const copyToClipboard = async (email: string) => {
    try {
      await navigator.clipboard.writeText(email);
      toast.success(`Email copied to clipboard: ${email}`);
    } catch (err) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = email;
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      try {
        document.execCommand('copy');
        toast.success(`Email copied to clipboard: ${email}`);
      } catch (fallbackErr) {
        toast.error('Failed to copy email to clipboard');
      }
      document.body.removeChild(textArea);
    }
  };

  useEffect(() => {
    const fetchTransactions = async () => {
      setIsLoading(true);
      try {
        await dispatch(getEventsGroupedByUser()).unwrap();
      } catch (error: any) {
        toast.error(error || "Failed to fetch transactions");
      } finally {
        setIsLoading(false);
      }
    };

    fetchTransactions();
  }, [dispatch]);

  const handleViewTransaction = (transaction: any) => {
    setSelectedUser({
      userDetails: transaction.userDetails,
      transactionDetails: transaction.transactionDetails,
      walletBalance: transaction.walletBalance
    });
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedUser(null);
  };

  if (isLoading || status === 'loading') {
    return (
      <main className="overflow-x-auto w-full">
        <div className="bg-white rounded-md shadow-md pb-6">
          <div className="p-6 flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading transactions...</p>
            </div>
          </div>
        </div>
      </main>
    );
  }

  if (error) {
    return (
      <main className="overflow-x-auto w-full">
        <div className="bg-white rounded-md shadow-md pb-6">
          <div className="p-6 flex items-center justify-center">
            <div className="text-center">
              <p className="text-red-600 mb-4">Error loading transactions: {error}</p>
              <button
                onClick={() => dispatch(getEventsGroupedByUser())}
                className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="overflow-x-auto w-full">
      <div className="bg-white rounded-md shadow-md pb-6">
        {/* Header */}
        <div className="p-6 border-b">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-semibold text-gray-800">Transactions</h1>
          </div>
        </div>

        {/* Transaction History Table */}
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-2">
              <h2 className="text-lg font-medium text-gray-800">Transaction History</h2>
              <span className="text-gray-500">→ Overview</span>
            </div>

            {/* Filters */}
            <div className="flex items-center gap-4">
              {/* User Type Filter */}
              <div className="flex items-center gap-2">
                <label htmlFor="userTypeFilter" className="text-sm font-medium text-gray-700">
                  User Type:
                </label>
                <select
                  id="userTypeFilter"
                  value={userTypeFilter}
                  onChange={(e) => setUserTypeFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500 text-sm"
                >
                  <option value="all">All Users</option>
                  <option value="flexible">Flexible</option>
                  <option value="outright">Outright</option>
                </select>
              </div>

              {/* Search Input */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaSearch className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search by email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500 text-sm"
                />
              </div>
            </div>
          </div>

          {filteredTransactions.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">
                {searchTerm || userTypeFilter !== "all"
                  ? `No transactions found${searchTerm ? ` for "${searchTerm}"` : ''}${userTypeFilter !== "all" ? ` in ${userTypeFilter} users` : ''}`
                  : "No transactions found"
                }
              </p>
              {(searchTerm || userTypeFilter !== "all") && (
                <div className="mt-2 space-x-2">
                  {searchTerm && (
                    <button
                      onClick={() => setSearchTerm("")}
                      className="text-green-600 hover:text-green-700 text-sm underline"
                    >
                      Clear search
                    </button>
                  )}
                  {userTypeFilter !== "all" && (
                    <button
                      onClick={() => setUserTypeFilter("all")}
                      className="text-green-600 hover:text-green-700 text-sm underline"
                    >
                      Show all user types
                    </button>
                  )}
                </div>
              )}
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="w-full" style={{ minWidth: "850px" }}>
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="p-3 text-left text-sm font-medium text-gray-700">S/N</th>
                      <th className="p-3 text-left text-sm font-medium text-gray-700">NAME</th>
                      <th className="p-3 text-left text-sm font-medium text-gray-700">E-Mail Address</th>
                      <th className="p-3 text-left text-sm font-medium text-gray-700">User Type</th>
                      <th className="p-3 text-left text-sm font-medium text-gray-700">Transactions</th>
                      <th className="p-3 text-left text-sm font-medium text-gray-700">Details</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {currentTransactions.map((transaction, index) => (
                      <tr key={transaction.id} className="hover:bg-gray-50">
                        <td className="p-3 text-sm text-gray-900">{String(startIndex + index + 1).padStart(3, '0')}</td>
                        <td className="p-3 text-sm text-gray-900">{transaction.name}</td>
                        <td className="p-3 text-sm text-gray-900">
                          <div className="flex items-center gap-2">
                            <span>{transaction.email}</span>
                            <button
                              onClick={() => copyToClipboard(transaction.email)}
                              className="text-gray-400 hover:text-green-600 transition-colors"
                              title="Copy email to clipboard"
                            >
                              <FaCopy className="w-3 h-3" />
                            </button>
                          </div>
                        </td>
                        <td className="p-3 text-sm text-gray-900">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            transaction.accountType === 'flexible'
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-green-100 text-green-800'
                          }`}>
                            {transaction.accountType?.charAt(0).toUpperCase() + transaction.accountType?.slice(1) || 'Flexible'}
                          </span>
                        </td>
                        <td className="p-3 text-sm text-gray-900">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            {transaction.transactionCount} transaction{transaction.transactionCount !== 1 ? 's' : ''}
                          </span>
                        </td>
                        <td className="p-3">
                          <button
                            onClick={() => handleViewTransaction(transaction)}
                            className="flex items-center gap-1 text-green-600 hover:text-green-700 font-medium text-sm"
                          >
                            <FaEye className="w-4 h-4" />
                            View
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-6 px-4">
                  <div className="text-sm text-gray-700">
                    Showing {startIndex + 1} to {Math.min(endIndex, filteredTransactions.length)} of {filteredTransactions.length} results
                    {(searchTerm || userTypeFilter !== "all") && (
                      <span className="text-green-600 ml-1">
                        (filtered by{searchTerm ? ` "${searchTerm}"` : ''}{userTypeFilter !== "all" ? ` ${userTypeFilter} users` : ''})
                      </span>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={handlePrevious}
                      disabled={currentPage === 1}
                      className="px-3 py-1 text-sm border rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>

                    {/* Page numbers */}
                    <div className="flex space-x-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        let pageNum: number;
                        if (totalPages <= 5) {
                          pageNum = i + 1;
                        } else if (currentPage <= 3) {
                          pageNum = i + 1;
                        } else if (currentPage >= totalPages - 2) {
                          pageNum = totalPages - 4 + i;
                        } else {
                          pageNum = currentPage - 2 + i;
                        }

                        return (
                          <button
                            key={pageNum}
                            onClick={() => handlePageChange(pageNum)}
                            className={`px-3 py-1 text-sm border rounded-md ${
                              currentPage === pageNum
                                ? 'bg-green-600 text-white border-green-600'
                                : 'hover:bg-gray-50'
                            }`}
                          >
                            {pageNum}
                          </button>
                        );
                      })}
                    </div>

                    <button
                      onClick={handleNext}
                      disabled={currentPage === totalPages}
                      className="px-3 py-1 text-sm border rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Transaction Modal */}
      {showModal && selectedUser && (
        <TransactionModal
          userDetails={selectedUser.userDetails}
          transactionDetails={selectedUser.transactionDetails}
          walletBalance={selectedUser.walletBalance}
          onClose={handleCloseModal}
        />
      )}
    </main>
  );
};

export default Transactions;
