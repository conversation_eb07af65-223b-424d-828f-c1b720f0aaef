import React, { useState } from "react";
import toast from "react-hot-toast";
import { FaTimes } from "react-icons/fa";
import { useAppSelector } from "../../redux/hooks";

interface CategoryType {
  name: string;
  _id: string;
}

interface Category {
  _id: string;
  category: string;
  categoryIcon: string;
  categoryType: CategoryType[];
}

interface AddTypeModalProps {
  category: Category;
  onClose: () => void;
  onTypeAdded: () => void;
}

const AddTypeModal: React.FC<AddTypeModalProps> = ({
  category,
  onClose,
  onTypeAdded,
}) => {
  const [typeName, setTypeName] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { token } = useAppSelector((store) => store.auth);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!typeName.trim()) {
      toast.error("Please enter a type name");
      return;
    }

    // Check if type already exists
    const typeExists = category.categoryType.some(
      (type) => type.name.toLowerCase() === typeName.trim().toLowerCase()
    );

    if (typeExists) {
      toast.error("This type already exists in the category");
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/onboardingCategory/${category._id}/categoryType`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: token || "",
          },
          body: JSON.stringify({ name: typeName.trim() }),
        }
      );

      if (response.ok) {
        toast.success("Type added successfully");
        onTypeAdded();
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to add type");
      }
    } catch (error) {
      console.error("Error adding type:", error);
      toast.error("Error adding type");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-800">
            Add Type to {category.category}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 p-1"
          >
            <FaTimes className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6">
          <div className="mb-4">
            <label
              htmlFor="typeName"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Type Name
            </label>
            <input
              type="text"
              id="typeName"
              value={typeName}
              onChange={(e) => setTypeName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent"
              placeholder="Enter type name"
              disabled={isLoading}
            />
          </div>

          {/* Existing Types */}
          {category.categoryType.length > 0 && (
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Existing Types
              </label>
              <div className="max-h-32 overflow-y-auto bg-gray-50 rounded-md p-2">
                {category.categoryType.map((type, index) => (
                  <div
                    key={type._id}
                    className="text-sm text-gray-600 py-1 px-2 bg-white rounded mb-1 last:mb-0"
                  >
                    {index + 1}. {type.name}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Buttons */}
          <div className="flex justify-end gap-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-secondary text-white rounded-md hover:bg-secondary/90 transition-colors disabled:opacity-50"
              disabled={isLoading}
            >
              {isLoading ? "Adding..." : "Add Type"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddTypeModal;
