import React, { useState } from "react";
import toast from "react-hot-toast";
import { FaTimes, FaExclamationTriangle } from "react-icons/fa";
import { useAppSelector } from "../../redux/hooks";

interface CategoryType {
  name: string;
  _id: string;
}

interface Category {
  _id: string;
  category: string;
  categoryIcon: string;
  categoryType: CategoryType[];
}

interface DeleteCategoryModalProps {
  category: Category;
  onClose: () => void;
  onCategoryDeleted: () => void;
}

const DeleteCategoryModal: React.FC<DeleteCategoryModalProps> = ({
  category,
  onClose,
  onCategoryDeleted,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [confirmText, setConfirmText] = useState("");
  const { token } = useAppSelector((store) => store.auth);

  const handleDelete = async () => {
    if (confirmText.toLowerCase() !== "delete") {
      toast.error("Please type 'delete' to confirm");
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/onboardingCategory/${category._id}`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
            Authorization: token || "",
          },
        }
      );

      if (response.ok) {
        toast.success("Category deleted successfully");
        onCategoryDeleted();
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to delete category");
      }
    } catch (error) {
      console.error("Error deleting category:", error);
      toast.error("Error deleting category");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center">
              <FaExclamationTriangle className="w-4 h-4 text-red-600" />
            </div>
            <h2 className="text-xl font-semibold text-gray-800">
              Delete Category
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 p-1"
          >
            <FaTimes className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="mb-4">
            <p className="text-gray-600 mb-2">
              Are you sure you want to delete the category{" "}
              <span className="font-semibold text-gray-800">
                "{category.category}"
              </span>
              ?
            </p>
            <p className="text-sm text-red-600 mb-4">
              This action cannot be undone. This will permanently delete the
              category and all its types ({category.categoryType.length} type
              {category.categoryType.length !== 1 ? "s" : ""}).
            </p>
          </div>

          {/* Category Types Preview */}
          {category.categoryType.length > 0 && (
            <div className="mb-4 p-3 bg-gray-50 rounded-md">
              <p className="text-sm font-medium text-gray-700 mb-2">
                Types that will be deleted:
              </p>
              <div className="max-h-32 overflow-y-auto">
                {category.categoryType.map((type, index) => (
                  <div
                    key={type._id}
                    className="text-sm text-gray-600 py-1"
                  >
                    {index + 1}. {type.name}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Confirmation Input */}
          <div className="mb-6">
            <label
              htmlFor="confirmText"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Type <span className="font-semibold">"delete"</span> to confirm:
            </label>
            <input
              type="text"
              id="confirmText"
              value={confirmText}
              onChange={(e) => setConfirmText(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
              placeholder="Type 'delete' to confirm"
              disabled={isLoading}
            />
          </div>

          {/* Buttons */}
          <div className="flex justify-end gap-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleDelete}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50"
              disabled={isLoading || confirmText.toLowerCase() !== "delete"}
            >
              {isLoading ? "Deleting..." : "Delete Category"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeleteCategoryModal;
