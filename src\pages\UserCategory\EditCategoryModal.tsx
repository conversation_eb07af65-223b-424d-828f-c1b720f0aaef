import React, { useState } from "react";
import toast from "react-hot-toast";
import { FaTimes, FaTrash } from "react-icons/fa";
import { useAppSelector } from "../../redux/hooks";

interface CategoryType {
  name: string;
  _id: string;
}

interface Category {
  _id: string;
  category: string;
  categoryIcon: string;
  categoryType: CategoryType[];
}

interface EditCategoryModalProps {
  category: Category;
  onClose: () => void;
  onCategoryUpdated: () => void;
}

const EditCategoryModal: React.FC<EditCategoryModalProps> = ({
  category,
  onClose,
  onCategoryUpdated,
}) => {
  const [categoryName, setCategoryName] = useState(category.category);
  const [categoryTypes, setCategoryTypes] = useState<CategoryType[]>(
    category.categoryType
  );
  const [newTypeName, setNewTypeName] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { token } = useAppSelector((store) => store.auth);

  const handleAddType = async () => {
    if (!newTypeName.trim()) {
      toast.error("Please enter a type name");
      return;
    }

    // Check if type already exists
    const typeExists = categoryTypes.some(
      (type) => type.name.toLowerCase() === newTypeName.trim().toLowerCase()
    );

    if (typeExists) {
      toast.error("This type already exists");
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/onboardingCategory/${category._id}/categoryType`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: token || "",
          },
          body: JSON.stringify({ name: newTypeName.trim() }),
        }
      );

      if (response.ok) {
        const data = await response.json();
        // Add the new type to local state
        setCategoryTypes([...categoryTypes, { name: newTypeName.trim(), _id: data.categoryType._id }]);
        setNewTypeName("");
        toast.success("Type added successfully");
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to add type");
      }
    } catch (error) {
      console.error("Error adding type:", error);
      toast.error("Error adding type");
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveType = async (typeId: string, typeName: string) => {
    if (categoryTypes.length <= 1) {
      toast.error("Category must have at least one type");
      return;
    }

    if (!window.confirm(`Are you sure you want to remove "${typeName}" type?`)) {
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/onboardingCategory/${category._id}/categoryType/${typeId}`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
            Authorization: token || "",
          },
        }
      );

      if (response.ok) {
        setCategoryTypes(categoryTypes.filter((type) => type._id !== typeId));
        toast.success("Type removed successfully");
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to remove type");
      }
    } catch (error) {
      console.error("Error removing type:", error);
      toast.error("Error removing type");
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateCategory = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!categoryName.trim()) {
      toast.error("Please enter a category name");
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/onboardingCategory/${category._id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: token || "",
          },
          body: JSON.stringify({
            category: categoryName.trim(),
            categoryIcon: category.categoryIcon,
          }),
        }
      );

      if (response.ok) {
        toast.success("Category updated successfully");
        onCategoryUpdated();
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to update category");
      }
    } catch (error) {
      console.error("Error updating category:", error);
      toast.error("Error updating category");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-800">
            Edit Category
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 p-1"
          >
            <FaTimes className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <div className="p-6">
          <form onSubmit={handleUpdateCategory} className="mb-6">
            <div className="mb-4">
              <label
                htmlFor="categoryName"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Category Name
              </label>
              <input
                type="text"
                id="categoryName"
                value={categoryName}
                onChange={(e) => setCategoryName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-secondary focus:border-transparent"
                placeholder="Enter category name"
                disabled={isLoading}
              />
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                className="px-4 py-2 bg-secondary text-white rounded-md hover:bg-secondary/90 transition-colors disabled:opacity-50"
                disabled={isLoading}
              >
                {isLoading ? "Updating..." : "Update Category"}
              </button>
            </div>
          </form>
          

          {/* Footer Buttons */}
          <div className="flex justify-end gap-3 mt-6 pt-6 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              disabled={isLoading}
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EditCategoryModal;
