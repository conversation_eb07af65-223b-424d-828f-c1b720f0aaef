import React, { useState, useEffect } from "react";
import { FaPlus, FaArrowLeft, FaEdit, FaTrash } from "react-icons/fa";
import toast from "react-hot-toast";
import CreateCategoryModal from "./CreateCategoryModal";
import AddTypeModal from "./AddTypeModal";

import { useAppSelector } from "../../redux/hooks";
import EditCategoryModal from "./EditCategoryModal";
import DeleteCategoryModal from "./DeleteCategoryModal";

interface CategoryType {
  name: string;
  _id: string;
}

interface Category {
  _id: string;
  category: string;
  categoryIcon: string;
  categoryType: CategoryType[];
}

interface User {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  employmentStatus: string;
  categoryType: string;
  bvn: string;
  isComplete: string;
  adminVerification: string;
  documentCheck: boolean;
  createdAt: string;
  ministryId?: string;
  stateGovernmentData?: {
    ministryId: string;
  };
  churchData?: {
    churchId: string;
  };
  privateOrganizationData?: {
    organizationId: string;
  };
  churchId?: string;
  organizationId?: string;
}

interface UserDataResponse {
  status: string;
  currentPage: number;
  totalPages: number;
  totalCustomers: number;
  data: User[];
}

const UserCategory: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showAddTypeModal, setShowAddTypeModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(
    null
  );
  const [categoryToEdit, setCategoryToEdit] = useState<Category | null>(null);
  const [categoryToDelete, setCategoryToDelete] = useState<Category | null>(null);
  const [selectedType, setSelectedType] = useState<CategoryType | null>(null);
  const [userData, setUserData] = useState<User[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const { token } = useAppSelector((store) => store.auth);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalUsers, setTotalUsers] = useState(0);

  const fetchCategories = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/onboardingCategories`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: token || "",
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        // Remove duplicates based on category name (case-insensitive)
        const uniqueCategories =
          data.categories?.filter(
            (category: Category, index: number, self: Category[]) =>
              index ===
              self.findIndex(
                (c) =>
                  c.category.toLowerCase() === category.category.toLowerCase()
              )
          ) || [];
        setCategories(uniqueCategories);
      } else {
        console.error("Failed to fetch categories. Status:", response.status);
        const errorData = await response.json().catch(() => ({}));
        console.error("Error response:", errorData);
        toast.error(errorData.message || "Failed to fetch categories");
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      toast.error("Error fetching categories");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  const handleCreateCategory = () => {
    setShowCreateModal(true);
  };

  const handleCategoryCreated = () => {
    fetchCategories();
    setShowCreateModal(false);
  };

  const handleAddType = (category: Category) => {
    setCategoryToEdit(category);
    setShowAddTypeModal(true);
  };

  const handleEditCategory = (category: Category) => {
    setCategoryToEdit(category);
    setShowEditModal(true);
  };

  const handleDeleteCategory = (category: Category) => {
    setCategoryToDelete(category);
    setShowDeleteModal(true);
  };

  const handleTypeAdded = () => {
    fetchCategories();
    setShowAddTypeModal(false);
    setCategoryToEdit(null);
  };

  const handleCategoryUpdated = () => {
    fetchCategories();
    setShowEditModal(false);
    setCategoryToEdit(null);
  };

  const handleCategoryDeleted = () => {
    // Add a small delay to ensure backend has processed the deletion
    setTimeout(() => {
      fetchCategories();
    }, 500);
    setShowDeleteModal(false);
    setCategoryToDelete(null);
  };

  const handleCategoryClick = (category: Category) => {
    setSelectedCategory(category);
  };

  const handleBackToCategories = () => {
    setSelectedCategory(null);
    setSelectedType(null);
    setUserData([]);
  };

  const handleBackToTypes = () => {
    setSelectedType(null);
    setUserData([]);
  };

  const fetchUserData = async (
    categoryType: string,
    filterValue: string,
    filterType:
      | "employmentStatus"
      | "ministryId"
      | "churchId"
      | "organizationId",
    page: number = 1
  ) => {
    // Prevent multiple simultaneous calls
    if (isLoadingUsers) {
      return;
    }

    setIsLoadingUsers(true);
    try {
      const response = await fetch(
        `${process.env.REACT_APP_API_URL}/getCustomersByCategoryType?categoryType=${categoryType}&page=${page}&limit=10`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: token || "",
          },
        }
      );

      if (response.ok) {
        const data: UserDataResponse = await response.json();

        // Filter based on the filter type
        let filteredData: User[] = [];

        if (filterType === "employmentStatus") {
          // Filter by employment status for individual category
          filteredData = data.data.filter(
            (user) =>
              user.employmentStatus.toLowerCase() === filterValue.toLowerCase()
          );
        } else if (filterType === "ministryId") {
          // Filter by ministry ID for state government category
          filteredData = data.data.filter(
            (user) =>
              user.stateGovernmentData?.ministryId === filterValue ||
              user.ministryId === filterValue
          );
        } else if (filterType === "churchId") {
          // Filter by church ID for church category
          filteredData = data.data.filter(
            (user) =>
              user.churchData?.churchId === filterValue ||
              user.churchId === filterValue
          );
        } else if (filterType === "organizationId") {
          // Filter by organization ID for private organization category
          filteredData = data.data.filter(
            (user) =>
              user.privateOrganizationData?.organizationId === filterValue ||
              user.organizationId === filterValue
          );
        }

        setUserData(filteredData);
        setCurrentPage(data.currentPage);
        setTotalPages(data.totalPages);
        setTotalUsers(filteredData.length);
      } else {
        if (response.status === 401) {
          toast.error("Unauthorized access. Please login again.");
        } else {
          toast.error("Failed to fetch user data");
        }
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
      toast.error("Error fetching user data");
    } finally {
      setIsLoadingUsers(false);
    }
  };

  const handleTypeClick = (type: CategoryType) => {
    // Prevent rapid clicks
    if (selectedCategory && !isLoadingUsers) {
      setSelectedType(type);

      const categoryType = selectedCategory.category.toLowerCase();

      if (categoryType === "individual") {
        // For individual category, filter by employment status
        const employmentStatusMap: { [key: string]: string } = {
          employed: "employed",
          student: "student",
          "self-employed": "selfEmployed",
        };

        const employmentStatus =
          employmentStatusMap[type.name.toLowerCase()] ||
          type.name.toLowerCase();

        fetchUserData(categoryType, employmentStatus, "employmentStatus");
      } else if (
        categoryType === "oyo state government" ||
        categoryType === "stategovernment"
      ) {
        // For state government category, filter by ministry ID
        fetchUserData("stateGovernment", type._id, "ministryId");
      } else if (categoryType === "church") {
        // For church category, filter by church ID
        fetchUserData("church", type._id, "churchId");
      } else if (
        categoryType === "private organization" ||
        categoryType === "privateorganization"
      ) {
        // For private organization category, filter by organization ID
        fetchUserData("privateOrganization", type._id, "organizationId");
      } else {
        // For other categories, default to employment status filtering
        fetchUserData(
          categoryType,
          type.name.toLowerCase(),
          "employmentStatus"
        );
      }
    }
  };

  // Helper function to get first two letters of category name
  const getCategoryInitials = (categoryName: string) => {
    return categoryName.substring(0, 2).toUpperCase();
  };

  // Show user data table if a type is selected
  if (selectedType && selectedCategory) {
    return (
      <div className="p-6 bg-gray-50 min-h-screen">
          <div className="bg-white rounded-lg shadow-md">
            {/* Header */}
            <div className="flex items-center p-6 border-b">
              <button
                onClick={handleBackToTypes}
                className="mr-4 text-gray-600 hover:text-gray-800 p-2 rounded-md hover:bg-gray-100"
              >
                <FaArrowLeft className="w-4 h-4" />
              </button>
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-full bg-secondary text-white flex items-center justify-center text-sm font-semibold">
                  {getCategoryInitials(selectedCategory.category)}
                </div>
                <h1 className="text-2xl font-bold text-gray-800">
                  {selectedCategory.category} - {selectedType.name} Users
                </h1>
              </div>
            </div>

          {/* User Data Table */}
          <div className="p-6">
            {isLoadingUsers ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-secondary"></div>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full table-auto">
                  <thead>
                    <tr className="bg-gray-50 border-b">
                      <th className="text-left p-4 font-semibold text-gray-700">
                        S/N
                      </th>
                      <th className="text-left p-4 font-semibold text-gray-700">
                        Name
                      </th>
                      <th className="text-left p-4 font-semibold text-gray-700">
                        Email
                      </th>
                      <th className="text-left p-4 font-semibold text-gray-700">
                        Phone
                      </th>
                      <th className="text-left p-4 font-semibold text-gray-700">
                        BVN
                      </th>
                      <th className="text-left p-4 font-semibold text-gray-700">
                        Verification
                      </th>
                      <th className="text-left p-4 font-semibold text-gray-700">
                        Date Joined
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {userData.length > 0 ? (
                      userData.map((user, index) => (
                        <tr
                          key={user._id}
                          className="border-b hover:bg-gray-50 transition-colors"
                        >
                          <td className="p-4 text-gray-600">{index + 1}</td>
                          <td className="p-4 font-medium text-gray-800">
                            {user.firstName} {user.lastName}
                          </td>
                          <td className="p-4 text-gray-600">{user.email}</td>
                          <td className="p-4 text-gray-600">
                            {user.phoneNumber}
                          </td>
                          <td className="p-4 text-gray-600">
                            {user.bvn || "---"}
                          </td>
                          
                          <td className="p-4">
                            <span
                              className={`px-2 py-1 rounded-full text-xs ${
                                user.documentCheck
                                  ? "bg-green-100 text-green-800"
                                  : "bg-red-100 text-red-800"
                              }`}
                            >
                              {user.documentCheck ? "Verified" : "Pending"}
                            </span>
                          </td>
                          <td className="p-4 text-gray-600">
                            {new Date(user.createdAt).toLocaleDateString(
                              "en-US",
                              {
                                year: "numeric",
                                month: "short",
                                day: "numeric",
                              }
                            )}
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td
                          colSpan={8}
                          className="text-center p-8 text-gray-500"
                        >
                          {selectedCategory.category.toLowerCase() ===
                            "church" ||
                          selectedCategory.category.toLowerCase() ===
                            "private organization" ? (
                            <div className="space-y-2">
                              <p>No users found for {selectedType.name}</p>
                              <p className="text-sm text-gray-400">
                                This category is currently empty. Users will
                                appear here once they register.
                              </p>
                            </div>
                          ) : (
                            `No users found for ${selectedType.name}`
                          )}
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}

            {/* Summary */}
            {userData.length > 0 && (
              <div className="mt-4 text-sm text-gray-600">
                Showing {userData.length} {selectedType.name.toLowerCase()} user
                {userData.length !== 1 ? "s" : ""}
              </div>
            )}
          </div>
        </div>

        {/* Modals */}
        {showAddTypeModal && categoryToEdit && (
          <AddTypeModal
            category={categoryToEdit}
            onClose={() => {
              setShowAddTypeModal(false);
              setCategoryToEdit(null);
            }}
            onTypeAdded={handleTypeAdded}
          />
        )}

        {showEditModal && categoryToEdit && (
          <EditCategoryModal
            category={categoryToEdit}
            onClose={() => {
              setShowEditModal(false);
              setCategoryToEdit(null);
            }}
            onCategoryUpdated={handleCategoryUpdated}
          />
        )}

        {showDeleteModal && categoryToDelete && (
          <DeleteCategoryModal
            category={categoryToDelete}
            onClose={() => {
              setShowDeleteModal(false);
              setCategoryToDelete(null);
            }}
            onCategoryDeleted={handleCategoryDeleted}
          />
        )}
      </div>
    );
  }

  // Show category types view if a category is selected
  if (selectedCategory) {
    return (
      <div className="p-6 bg-gray-50 min-h-screen">
        <div className="bg-white rounded-lg shadow-md">
          {/* Header */}
          <div className="flex items-center p-6 border-b">
            <button
              onClick={handleBackToCategories}
              className="mr-4 text-gray-600 hover:text-gray-800 p-2 rounded-md hover:bg-gray-100"
            >
              <FaArrowLeft className="w-4 h-4" />
            </button>
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-full bg-secondary text-white flex items-center justify-center text-sm font-semibold">
                  {getCategoryInitials(selectedCategory.category)}
                </div>
                <h1 className="text-2xl font-bold text-gray-800">
                  {selectedCategory.category} Types
                </h1>
              </div>
              <button
                onClick={() => handleAddType(selectedCategory)}
                className="bg-secondary text-white px-4 py-2 rounded-md hover:bg-secondary/90 transition-colors flex items-center gap-2"
              >
                <FaPlus className="w-4 h-4" />
                Add Type
              </button>
            </div>
          </div>

          {/* Category Types List */}
          <div className="p-6">
            <div className="grid gap-3">
              {selectedCategory.categoryType.map((type, index) => (
                <div
                  key={type._id}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                  onClick={() => handleTypeClick(type)}
                >
                  <div className="flex items-center gap-3">
                    <span className="bg-secondary text-white text-sm px-3 py-1 rounded-full">
                      {index + 1}
                    </span>
                    <span className="font-medium text-gray-800 hover:text-secondary">
                      {type.name}
                    </span>
                  </div>
                  <span className="text-gray-400 text-sm">
                    Click to view users →
                  </span>
                </div>
              ))}
              {selectedCategory.categoryType.length === 0 && (
                <div className="text-center p-8 text-gray-500">
                  No types found for this category
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Modals */}
        {showAddTypeModal && categoryToEdit && (
          <AddTypeModal
            category={categoryToEdit}
            onClose={() => {
              setShowAddTypeModal(false);
              setCategoryToEdit(null);
            }}
            onTypeAdded={handleTypeAdded}
          />
        )}

        {showEditModal && categoryToEdit && (
          <EditCategoryModal
            category={categoryToEdit}
            onClose={() => {
              setShowEditModal(false);
              setCategoryToEdit(null);
            }}
            onCategoryUpdated={handleCategoryUpdated}
          />
        )}

        {showDeleteModal && categoryToDelete && (
          <DeleteCategoryModal
            category={categoryToDelete}
            onClose={() => {
              setShowDeleteModal(false);
              setCategoryToDelete(null);
            }}
            onCategoryDeleted={handleCategoryDeleted}
          />
        )}
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="bg-white rounded-lg shadow-md">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <h1 className="text-2xl font-bold text-gray-800">User Categories</h1>
          <button
            onClick={handleCreateCategory}
            className="bg-secondary text-white px-4 py-2 rounded-md flex items-center gap-2 hover:bg-secondary/90 transition-colors"
          >
            <FaPlus className="w-4 h-4" />
            Create Category
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-secondary"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full table-auto">
                <thead>
                  <tr className="bg-gray-50 border-b">
                    <th className="text-left p-4 font-semibold text-gray-700">
                      S/N
                    </th>
                    <th className="text-left p-4 font-semibold text-gray-700">
                      Category
                    </th>
                    <th className="text-left p-4 font-semibold text-gray-700">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {categories.length > 0 ? (
                    categories.map((category, index) => (
                      <tr
                        key={category._id}
                        className="border-b hover:bg-gray-50 transition-colors"
                      >
                        <td className="p-4 text-gray-600">{index + 1}</td>
                        <td
                          className="p-4 font-medium text-gray-800 hover:text-secondary cursor-pointer"
                          onClick={() => handleCategoryClick(category)}
                        >
                          <div className="flex items-center justify-between">
                            <span>
                              {category.category.charAt(0).toUpperCase() +
                                category.category.slice(1)}
                            </span>
                            <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                              {category.categoryType.length} type
                              {category.categoryType.length !== 1 ? "s" : ""}
                            </span>
                          </div>
                        </td>
                        <td className="p-4">
                          <div className="flex items-center gap-2">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleAddType(category);
                              }}
                              className="text-blue-600 hover:text-blue-800 p-1 rounded transition-colors"
                              title="Add Type"
                            >
                              <FaPlus className="w-3 h-3" />
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEditCategory(category);
                              }}
                              className="text-green-600 hover:text-green-800 p-1 rounded transition-colors"
                              title="Edit Category"
                            >
                              <FaEdit className="w-3 h-3" />
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteCategory(category);
                              }}
                              className="text-red-600 hover:text-red-800 p-1 rounded transition-colors"
                              title="Delete Category"
                            >
                              <FaTrash className="w-3 h-3" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={3} className="text-center p-8 text-gray-500">
                        No categories found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Create Category Modal */}
      {showCreateModal && (
        <CreateCategoryModal
          onClose={() => setShowCreateModal(false)}
          onCategoryCreated={handleCategoryCreated}
        />
      )}

      {/* Add Type Modal */}
      {showAddTypeModal && categoryToEdit && (
        <AddTypeModal
          category={categoryToEdit}
          onClose={() => {
            setShowAddTypeModal(false);
            setCategoryToEdit(null);
          }}
          onTypeAdded={handleTypeAdded}
        />
      )}

      {/* Edit Category Modal */}
      {showEditModal && categoryToEdit && (
        <EditCategoryModal
          category={categoryToEdit}
          onClose={() => {
            setShowEditModal(false);
            setCategoryToEdit(null);
          }}
          onCategoryUpdated={handleCategoryUpdated}
        />
      )}

      {/* Delete Category Modal */}
      {showDeleteModal && categoryToDelete && (
        <DeleteCategoryModal
          category={categoryToDelete}
          onClose={() => {
            setShowDeleteModal(false);
            setCategoryToDelete(null);
          }}
          onCategoryDeleted={handleCategoryDeleted}
        />
      )}
    </div>
  );
};

export default UserCategory;
