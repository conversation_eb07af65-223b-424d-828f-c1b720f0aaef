import { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../redux/hooks";
import { <PERSON>a<PERSON><PERSON>, FaWhatsapp } from "react-icons/fa6";
import UpdateCreditLimit from "../../components/modals/UpdateCreditLimit";
import Pagination from "../../components/Pagination";
import { AllBanksModal } from "../../components/modals/AllBanksModal";
import { getFlexibleUsers, getOutrightUsers } from "../../redux/thunk";
import LoadingSpinner from "../../components/elements/LoadingSpinner";

const UserCategoryTable = () => {
  const [activeTab, setActiveTab] = useState("individuals");
  const [userId, setUserId] = useState("");
  const [allBanksModal, setAllBanksModal] = useState(false);
  const [allBanksDetails, setAllBanksDetails] = useState([]);
  const [userData, setUserData] = useState([]);
  const [viewUpdateCreditLimit, setViewUpdateCreditLimit] = useState(false);
  const [searchedUser, setSearchedUser] = useState("");
  const [viewAddress, setViewAddress] = useState({ show: false, address: "" });
  const dispatch = useAppDispatch();

  const { flexible, outright, status } = useAppSelector((store) => store.users);
  const data =
    activeTab === "individuals"
      ? [...flexible, ...outright]
      : activeTab === "state"
      ? []
      : activeTab === "microfinance"
      ? []
      : activeTab === "church"
      ? []
      : [];

  useEffect(() => {
    dispatch(getFlexibleUsers());
    dispatch(getOutrightUsers());
  }, []);

  const handleViewAllBank = (id: string, linkedBanks: any) => {
    setUserId(id);
    setAllBanksDetails(linkedBanks);
    setAllBanksModal((prev) => !prev);
  };

  const handleUpdateCreditLimit = (data: any) => {
    setUserData(data);
    setViewUpdateCreditLimit((prev) => !prev);
  };

  const [itemsPerPage] = useState(20);
  const [currentPage, setCurrentPage] = useState(1);

  const indexOfLastPost = currentPage * itemsPerPage;
  const indexOfFirstPost = indexOfLastPost - itemsPerPage;
  const currentItems = data.slice(indexOfFirstPost, indexOfLastPost);

  const handlePagination = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const prevPage = () => {
    if (currentPage !== 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const nextPage = () => {
    if (currentPage !== Math.ceil(data.length / itemsPerPage)) {
      setCurrentPage(currentPage + 1);
    }
  };

  return (
    <section>
      <div className="flex gap-10 mb-6">
        <span
          className={`cursor-pointer pb-2 ${
            activeTab === "individuals" ? "border-b-2 border-green-500" : "null"
          }`}
          onClick={() => setActiveTab("individuals")}
        >
          Individuals
        </span>
        <span
          className={`cursor-pointer pb-2 ${
            activeTab === "state" ? "border-b-2 border-green-500" : "null"
          }`}
          onClick={() => setActiveTab("state")}
        >
          State
        </span>
        <span
          className={`cursor-pointer pb-2 ${
            activeTab === "microfinance"
              ? "border-b-2 border-green-500"
              : "null"
          }`}
          onClick={() => setActiveTab("microfinance")}
        >
          Micro Finance
        </span>
        <span
          className={`cursor-pointer pb-2 ${
            activeTab === "church" ? "border-b-2 border-green-500" : "null"
          }`}
          onClick={() => setActiveTab("church")}
        >
          Church
        </span>
        <span
          className={`cursor-pointer pb-2 ${
            activeTab === "privateOrg" ? "border-b-2 border-green-500" : "null"
          }`}
          onClick={() => setActiveTab("privateOrg")}
        >
          Private Organization
        </span>
      </div>
      <section className="overflow-x-auto">
        <table className="text-sm font-mont w-full">
          <thead>
            <tr className="bg-[#E9EAEFC2] p-2">
              <th className="p-2 text-nowrap">SN</th>
              <th className="p-2 text-nowrap">Name</th>
              <th className="p-2 text-nowrap">Email</th>
              <th className="p-2 text-nowrap">Phone Number</th>
              <th className="p-2 text-nowrap">Contact</th>
              {activeTab === "state" && (
                <th className="p-2 text-nowrap">Office Address</th>
              )}
              <th className="p-2 text-nowrap">All Banks</th>
              <th className="p-2 text-nowrap">Credit Score Bal.</th>
              <th className="p-2 text-nowrap">Credit Score</th>
              <th className="p-2 text-nowrap">Open Password</th>
              <th className="p-2 text-nowrap">Action</th>
            </tr>
          </thead>
          <tbody>
            {status === "loading" ? (
              <tr>
                <td colSpan={10} className="text-center">
                  <LoadingSpinner />
                </td>
              </tr>
            ) : currentItems.length > 0 ? (
              currentItems.map((user, index) => (
                <tr key={index}>
                  <td className="p-2 text-center">
                    {index + indexOfFirstPost + 1}
                  </td>
                  <td className="p-2 text-nowrap capitalize">
                    {user?.lastName?.toLowerCase()}{" "}
                    {user?.firstName?.toLowerCase()}
                  </td>
                  <td className="p-2">{user?.email}</td>
                  <td className="p-2 text-nowrap">
                    {user?.phoneNumber || "No number"}
                  </td>
                  <td className="p-2">
                    <button
                      className="mr-3"
                      type="button"
                      title="call customer"
                    >
                      <a href={`tel:${user?.phoneNumber}`}>
                        <FaPhone />
                      </a>
                    </button>
                    <button type="button" title="text via whatsapp">
                      <a
                        href={`https://wa.me/${user?.phoneNumber?.replace(
                          /^0/,
                          "234"
                        )}`}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <FaWhatsapp />
                      </a>
                    </button>
                  </td>
                  <td className="p-2 text-nowrap">
                    <button
                      type="button"
                      className="text-secondary text-sm border border-secondary p-2"
                      onClick={() =>
                        setViewAddress({
                          show: true,
                          address: user?.officeAddress,
                        })
                      }
                    >
                      View
                    </button>
                  </td>

                  <td className="p-2 text-center">
                    <button
                      className="bg-secondary text-white text-sm rounded-md p-2"
                      onClick={() =>
                        handleViewAllBank(user?._id, user?.linkedBanks)
                      }
                    >
                      View
                    </button>
                  </td>
                  <td className="p-2">
                    ₦
                    {user?.creditScore < 0
                      ? "0"
                      : user?.creditScore?.toLocaleString()}
                  </td>
                  <td className="p-2">
                    ₦{user?.creditLimit?.toLocaleString()}
                  </td>
                  <td className="p-2">
                    {user?.userPassword ? user?.userPassword : "not found"}
                  </td>
                  <td className="p-2 text-nowrap">
                    <button
                      type="button"
                      className="p-2 text-sm text-secondary border border-secondary"
                      onClick={() => handleUpdateCreditLimit(user)}
                    >
                      Update Credit Limit
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={9} className="text-center text-sm">
                  No Data to show
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </section>
      <section className="p-3 my-5">
        <Pagination
          length={data.length > 0 ? data.length : 0}
          itemsPerPage={itemsPerPage}
          handlePagination={handlePagination}
          currentPage={currentPage}
          prevPage={prevPage}
          nextPage={nextPage}
        />
      </section>
      {viewUpdateCreditLimit && (
        <UpdateCreditLimit
          userData={userData}
          closeModal={() => setViewUpdateCreditLimit(false)}
        />
      )}
      {viewAddress.show && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-5 rounded shadow-lg w-1/3">
            <h2 className="text-lg font-bold mb-4">User Address</h2>
            <p className="text-wrap">
              {viewAddress.address || "No address available"}
            </p>
            <button
              className="mt-4 bg-secondary text-white px-4 py-2 rounded"
              onClick={() => setViewAddress({ show: false, address: "" })}
            >
              Close
            </button>
          </div>
        </div>
      )}
      {allBanksModal && (
        <AllBanksModal
          setAllBanksModal={setAllBanksModal}
          setAllBanksDetails={setAllBanksDetails}
          allBanksDetails={allBanksDetails}
          setSearchedUser={setSearchedUser}
          userId={userId}
        />
      )}
    </section>
  );
};

export default UserCategoryTable;
