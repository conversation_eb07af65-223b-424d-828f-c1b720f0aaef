import { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../redux/hooks";
import { IoAdd, IoSearchOutline } from "react-icons/io5";
import { AllBanksModal } from "../../components/modals/AllBanksModal";
import { AddBankModal } from "../../components/modals/AddBankModal";
import BVNVerificationModal from "../../components/modals/BVNVerificationModal";
import SelfEmployed from "../../components/modals/SelfEmployed";
import Employed from "../../components/modals/Employed";
import Student from "../../components/modals/Student";
import Pagination from "../../components/Pagination";
import toast from "react-hot-toast";
import { FaPhone, FaWhatsapp } from "react-icons/fa6";
import UpdateCreditLimit from "../../components/modals/UpdateCreditLimit";
import StateUsers from "../../components/users/StateUsers";
import { getFlexibleUsers, getOutrightUsers } from "../../redux/thunk";

// Helper function to display business owner status
const formatBusinessOwner = (isBusinessOwner: boolean | undefined | null): string => {
  // Handle cases where the field might not exist in older user records
  if (isBusinessOwner === undefined || isBusinessOwner === null) {
    return "Not specified";
  }
  return isBusinessOwner ? "Yes" : "No";
};

const UserDetails = () => {
  const [bvnModalOpen, setBvnModalOpen] = useState(false);
  const [studentModalOpen, setStudentModalOpen] = useState(false);
  const [filter, setFilter] = useState("all");
  const [selectedBvn, setSelectedBvn] = useState("");
  const [selectedInfo, setSelectedInfo] = useState<any>({});
  const [activeTab, setActiveTab] = useState("flexible");
  const [selfEmployedModalOpen, setSelfEmployedModalOpen] = useState(false);
  const [allBanksModal, setAllBanksModal] = useState(false);
  const [allBanksDetails, setAllBanksDetails] = useState([]);
  const [employedModalOpen, setEmployedModalOpen] = useState(false);
  const [userId, setUserId] = useState("");
  const [addBankModal, setAddBankModal] = useState(false);
  const [userToAddBank, setUserToAddBank] = useState({});
  const [searchedUser, setSearchedUser] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [userData, setUserData] = useState([]);
  const [viewUpdateCreditLimit, setViewUpdateCreditLimit] = useState(false);

  const dispatch = useAppDispatch();
  const { flexible, outright, status } = useAppSelector((store) => store.users);

  const [itemsPerPage] = useState(20);
  const [flexibleCurrentPage, setFlexibleCurrentPage] = useState(1);
  const [outrightCurrentPage, setOutrightCurrentPage] = useState(1);
  const flexibleIndexOfLastPost = flexibleCurrentPage * itemsPerPage;
  const outrightIndexOfLastPost = outrightCurrentPage * itemsPerPage;
  const flexibleIndexOfFirstPost = flexibleIndexOfLastPost - itemsPerPage;
  const outrightIndexOfFirstPost = outrightIndexOfLastPost - itemsPerPage;

  const flexibleIndividuals = flexible
    .slice()
    .reverse()
    .filter((user: any) => user.categoryType !== "stateGovernment");
  const flexibleCurrentItems = flexibleIndividuals.slice(
    flexibleIndexOfFirstPost,
    flexibleIndexOfLastPost
  );
  const outrightCurrentItems = outright.slice(
    outrightIndexOfFirstPost,
    outrightIndexOfLastPost
  );

  // Pagination
  const handlePaginationFlexible = (pageNumber: number) => {
    setFlexibleCurrentPage(pageNumber);
  };
  const handlePaginationOutright = (pageNumber: number) => {
    setOutrightCurrentPage(pageNumber);
  };

  const prevPageFlexible = () => {
    if (flexibleCurrentPage !== 1) {
      setFlexibleCurrentPage(flexibleCurrentPage - 1);
    }
  };

  const nextPageFlexible = () => {
    if (
      flexibleCurrentPage !==
      Math.ceil(flexibleIndividuals.length / itemsPerPage)
    ) {
      setFlexibleCurrentPage(flexibleCurrentPage + 1);
    }
  };
  const prevPageOutright = () => {
    if (outrightCurrentPage !== 1) {
      setOutrightCurrentPage(outrightCurrentPage - 1);
    }
  };

  const nextPageOutright = () => {
    if (outrightCurrentPage !== Math.ceil(outright.length / itemsPerPage)) {
      setOutrightCurrentPage(outrightCurrentPage + 1);
    }
  };

  const handleBvnClick = (bvn: string) => {
    setSelectedBvn(bvn);
    setBvnModalOpen(true);
  };

  const handleViewAllBank = (id: string, linkedBanks: any) => {
    setUserId(id);
    setAllBanksDetails(linkedBanks);
    setAllBanksModal((prev) => !prev);
  };

  const handleUpdateCreditLimit = (data: any) => {
    setUserData(data);
    setViewUpdateCreditLimit((prev) => !prev);
  };

  const handleJobClick = (details: string, occupation: any) => {
    setSelectedInfo(details);
    if (occupation === "Self-Employed") {
      setSelfEmployedModalOpen(true);
    } else if (occupation === "Employed") {
      setEmployedModalOpen(true);
    } else {
      setStudentModalOpen(true);
    }
  };

  useEffect(() => {
    dispatch(getFlexibleUsers());
    dispatch(getOutrightUsers());
  }, []);

  const handleAddBank = (firstName: any, lastName: any, id: string) => {
    setUserToAddBank({ name: lastName + " " + firstName, id });
    setAddBankModal((prev) => !prev);
  };

  const searchForUsers = (value: string) => {
    setSearchedUser(value);
    if (value.length > 0) {
      const isPhoneNumber = /^\d+$/.test(value);
      const searchResult =
        activeTab === "flexible"
          ? flexibleIndividuals?.filter((user: any) =>
              isPhoneNumber
                ? user.phoneNumber?.toLowerCase().includes(value.toLowerCase())
                : user.email?.toLowerCase().includes(value.toLowerCase())
            ) || []
          : outright?.filter((user: any) =>
              isPhoneNumber
                ? user.phoneNumber?.toLowerCase().includes(value.toLowerCase())
                : user.email?.toLowerCase().includes(value.toLowerCase())
            ) || [];
      setSearchResults(searchResult);
    } else {
      setSearchResults([]);
    }
  };

  return (
    <main className="overflow-x-auto w-full">
      <div className="flex gap-10 mb-6">
        <span
          className={`cursor-pointer pb-2 ${
            activeTab === "flexible" ? "border-b-2 border-green-500" : "null"
          }`}
          onClick={() => setActiveTab("flexible")}
        >
          Flexible Customers
        </span>
        <span
          className={`cursor-pointer pb-2 ${
            activeTab === "outright" ? "border-b-2 border-green-500" : "null"
          }`}
          onClick={() => setActiveTab("outright")}
        >
          Outright Customers
        </span>
        <span
          className={`cursor-pointer pb-2 ${
            activeTab === "stateUsers" ? "border-b-2 border-green-500" : "null"
          }`}
          onClick={() => setActiveTab("stateUsers")}
        >
          State Users
        </span>
      </div>
      {activeTab === "flexible" ? (
        <div className="bg-white rounded-md shadow-md pb-6">
          <div
            className={`w-full overflow-x-auto ${
              status === "loading" && "animate-pulse h-[50vh]"
            }`}
          >
            <div
              className="flex items-center justify-between p-6"
              style={{ minWidth: "700px" }}
            >
              <div className="flex justify-between w-full">
                <h1 className="text-base font-semibold ">User Details</h1>
                <div className="relative md:w-[30rem] w-fit">
                  <IoSearchOutline className="w-6 h-6 absolute top-[0.6rem] left-2 text-gray-300" />
                  <input
                    type="search"
                    name="searchedUser"
                    id="searchedUser"
                    value={searchedUser}
                    onChange={(e) => searchForUsers(e.target.value)}
                    placeholder="Search with email or phone number"
                    className="border p-2 text-sm rounded-md indent-7 w-full"
                  />
                </div>
                <label htmlFor="filter">
                  Sort:
                  <select
                    name="filter"
                    id="filter"
                    className="border p-2 ml-4"
                    onChange={(e) => setFilter(e.target.value)}
                  >
                    <option value="all">All</option>
                    <option value="employed">Employed</option>
                    <option value="self-employed">Self-employed</option>
                    <option value="student">Students</option>
                  </select>
                </label>
              </div>
            </div>
            <section className="overflow-x-auto">
              <table className="w-[1200px]" style={{ minWidth: "700px" }}>
                <thead className="bg-gray-50 font-bold p-4 text-left">
                  {filter === "student" && (
                    <tr>
                      <th className="p-2">S/N</th>
                      <th className="p-2 text-nowrap">Name</th>
                      <th className="p-2 text-nowrap">Email</th>
                      <th className="p-2 text-nowrap">Phone Number</th>
                      <th className="p-2 text-nowrap">Contact</th>
                      <th className="p-2 text-nowrap">Add Bank</th>
                      <th className="p-2 text-nowrap">All Banks</th>
                      <th className="p-2 text-nowrap">Business Owner</th>
                      <th className="p-2 text-nowrap">Credit Score(Balance)</th>
                      <th className="p-2 text-nowrap">Credit Score</th>
                      <th className="p-2 text-nowrap">Action</th>
                    </tr>
                  )}
                  {filter === "employed" && (
                    <tr>
                      <th className="p-2 ">S/N</th>
                      <th className="p-2 text-nowrap">Name</th>
                      <th className="p-2 text-nowrap">Email</th>
                      <th className="p-2 text-nowrap">Phone Number</th>
                      <th className="p-2 text-nowrap">Contact</th>
                      <th className="p-2 text-nowrap">Add Bank</th>
                      <th className="p-2 text-nowrap">All Banks</th>
                      <th className="p-2 text-nowrap">Business Owner</th>
                      <th className="p-2 text-nowrap">Credit Score(Balance)</th>
                      <th className="p-2 text-nowrap">Credit Score</th>
                      <th className="p-2 text-nowrap">Action</th>
                    </tr>
                  )}
                  {filter === "all" && (
                    <tr>
                      <th className="p-2 ">S/N</th>
                      <th className="p-2 text-nowrap">Name</th>
                      <th className="p-2 text-nowrap">Email</th>
                      <th className="p-2 text-nowrap">Phone Number</th>
                      <th className="p-2 text-nowrap">Contact</th>
                      <th className="p-2 text-nowrap">Add Bank</th>
                      <th className="p-2 text-nowrap">All Banks</th>
                      <th className="p-2 text-nowrap">Business Owner</th>
                      <th className="p-2 text-nowrap">Credit Score(Balance)</th>
                      <th className="p-2 text-nowrap">Credit Score</th>
                      <th className="p-2 text-nowrap">Action</th>
                    </tr>
                  )}
                  {filter === "self-employed" && (
                    <tr>
                      <th className="p-2 ">S/N</th>
                      <th className="p-2 text-nowrap">Name</th>
                      <th className="p-2 text-nowrap">Email</th>
                      <th className="p-2 text-nowrap">Phone Number</th>
                      <th className="p-2 text-nowrap">Contact</th>
                      <th className="p-2 text-nowrap">Add Bank</th>
                      <th className="p-2 text-nowrap">All Banks</th>
                      <th className="p-2 text-nowrap">Business Owner</th>
                      <th className="p-2 text-nowrap">Credit Score(Balance)</th>
                      <th className="p-2 text-nowrap">Credit Score</th>
                      <th className="p-2 text-nowrap">Action</th>
                    </tr>
                  )}
                </thead>
                <tbody className="px-4">
                  {searchedUser.length > 0 &&
                  searchResults.length > 0 &&
                  activeTab === "flexible"
                    ? searchResults.map((data: any, index) => (
                        <tr
                          className="border-b border-gray-300 py-2"
                          key={index}
                        >
                          <td className="text-secondary p-2">
                            {index + flexibleIndexOfFirstPost + 1}
                          </td>
                          <td className="p-2 text-nowrap capitalize">
                            {data.lastName.toLowerCase()}{" "}
                            {data.firstName.toLowerCase()}
                          </td>
                          <td className="p-2">{data.email}</td>
                          <td className="p-2 text-nowrap">
                            {data?.phoneNumber || "No number"}
                          </td>
                          <td className="p-2">
                            <button
                              className="mr-3"
                              type="button"
                              title="call customer"
                            >
                              <a href={`tel:${data?.phoneNumber}`}>
                                <FaPhone />
                              </a>
                            </button>
                            <button type="button" title="text via whatsapp">
                              <a
                                href={`https://wa.me/${data?.phoneNumber?.replace(
                                  /^0/,
                                  "234"
                                )}`}
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                <FaWhatsapp />
                              </a>
                            </button>
                          </td>
                          <td className="p-2 text-center">
                            <button
                              className="text-secondary border"
                              onClick={() =>
                                handleAddBank(
                                  data.firstName,
                                  data.lastName,
                                  data._id
                                )
                              }
                            >
                              <IoAdd className="w-9 h-9" />
                            </button>
                          </td>
                          <td className="p-2 text-center">
                            <button
                              className="bg-secondary text-white text-sm rounded-md p-2"
                              onClick={() =>
                                handleViewAllBank(data._id, data.linkedBanks)
                              }
                            >
                              View
                            </button>
                          </td>
                          <td className="p-2 text-nowrap">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              formatBusinessOwner(data.isBusinessOwner) === 'Yes'
                                ? 'bg-green-100 text-green-800'
                                : formatBusinessOwner(data.isBusinessOwner) === 'No'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {formatBusinessOwner(data.isBusinessOwner)}
                            </span>
                          </td>
                          <td className="p-2">
                            ₦
                            {data.creditScore < 0
                              ? "0"
                              : data.creditScore.toLocaleString()}
                          </td>
                          <td className="p-2">
                            ₦{data.creditLimit.toLocaleString()}
                          </td>
                          <td className="p-2 text-nowrap">
                            <button
                              type="button"
                              className="p-2 text-sm text-secondary border border-secondary"
                              onClick={() => handleUpdateCreditLimit(data)}
                            >
                              Update Credit Limit
                            </button>
                          </td>
                        </tr>
                      ))
                    : flexibleCurrentItems
                        .filter((customer: any) => {
                          if (filter === "employed") {
                            return customer.employmentStatus === "employed";
                          } else if (filter === "self-employed") {
                            return customer.employmentStatus === "selfEmployed";
                          } else if (filter === "student") {
                            return customer.employmentStatus === "student";
                          } else if (filter === "all") {
                            return customer.employmentStatus;
                          }
                          return true;
                        })
                        .map((data: any, index: number) => {
                          return (
                            <tr
                              className="border-b border-gray-300 py-2"
                              key={index}
                            >
                              <td className="text-secondary p-2">
                                {index + flexibleIndexOfFirstPost + 1}
                              </td>
                              <td className="p-2 text-nowrap capitalize">
                                {data.lastName.toLowerCase()}{" "}
                                {data.firstName.toLowerCase()}
                              </td>
                              <td className="p-2">{data.email}</td>
                              <td className="p-2">
                                {data.phoneNumber
                                  ? data.phoneNumber
                                  : "No number"}
                              </td>
                              <td className="p-2">
                                <button
                                  className="mr-3"
                                  type="button"
                                  title="call customer"
                                >
                                  <a href={`tel:${data?.phoneNumber}`}>
                                    <FaPhone />
                                  </a>
                                </button>
                                <button type="button" title="text via whatsapp">
                                  <a
                                    href={`https://wa.me/${data?.phoneNumber?.replace(
                                      /^0/,
                                      "234"
                                    )}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                  >
                                    <FaWhatsapp />
                                  </a>
                                </button>
                              </td>
                              <td className="p-2 text-center">
                                <button
                                  className="text-secondary border"
                                  onClick={() =>
                                    handleAddBank(
                                      data.firstName,
                                      data.lastName,
                                      data._id
                                    )
                                  }
                                >
                                  <IoAdd className="w-9 h-9" />
                                </button>
                              </td>
                              <td className="p-2 text-center">
                                <button
                                  className="bg-secondary text-white text-sm rounded-md p-2"
                                  onClick={() =>
                                    handleViewAllBank(
                                      data._id,
                                      data.linkedBanks
                                    )
                                  }
                                >
                                  View
                                </button>
                              </td>
                              <td className="p-2 text-nowrap">
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  formatBusinessOwner(data.isBusinessOwner) === 'Yes'
                                    ? 'bg-green-100 text-green-800'
                                    : formatBusinessOwner(data.isBusinessOwner) === 'No'
                                    ? 'bg-red-100 text-red-800'
                                    : 'bg-gray-100 text-gray-800'
                                }`}>
                                  {formatBusinessOwner(data.isBusinessOwner)}
                                </span>
                              </td>
                              <td className="p-2">
                                ₦
                                {data.creditScore < 0
                                  ? "0"
                                  : data.creditScore.toLocaleString()}
                              </td>
                              <td className="p-2">
                                ₦{data.creditLimit.toLocaleString()}
                              </td>
                              <td className="p-2 text-nowrap">
                                <button
                                  type="button"
                                  className="p-2 text-sm text-secondary border border-secondary"
                                  onClick={() => handleUpdateCreditLimit(data)}
                                >
                                  Update Credit Limit
                                </button>
                              </td>
                            </tr>
                          );
                        })}
                </tbody>
              </table>
            </section>
          </div>
          <section className="p-3 my-5">
            <Pagination
              length={
                searchResults.length > 0
                  ? searchResults.length
                  : flexibleIndividuals.length
              }
              itemsPerPage={itemsPerPage}
              handlePagination={handlePaginationFlexible}
              currentPage={flexibleCurrentPage}
              prevPage={prevPageFlexible}
              nextPage={nextPageFlexible}
            />
          </section>
        </div>
      ) : activeTab === "stateUsers" ? (
        <StateUsers
          stateUsers={flexible
            .filter((user: any) => user.categoryType === "stateGovernment")
            .slice()
            .reverse()}
          handleBvnClick={handleBvnClick}
          handleViewAllBank={handleViewAllBank}
          handleAddBank={handleAddBank}
          handleUpdateCreditLimit={handleUpdateCreditLimit}
        />
      ) : (
        <div className="bg-white rounded-md shadow-md pb-6">
          <div
            className={`w-full overflow-x-auto ${
              status === "loading" && "animate-pulse h-[50vh]"
            }`}
          >
            <div
              className="flex items-center justify-between p-6"
              style={{ minWidth: "700px" }}
            >
              <div className="flex justify-between w-full">
                <h1 className="text-base font-semibold ">Outright Customers</h1>
                <div className="relative md:w-[30rem] w-fit">
                  <IoSearchOutline className="w-6 h-6 absolute top-[0.6rem] left-2 text-gray-300" />
                  <input
                    type="search"
                    name="searchedUser"
                    id="searchedUser"
                    value={searchedUser}
                    onChange={(e) => searchForUsers(e.target.value)}
                    placeholder="Search user using email"
                    className="border p-2 rounded-md indent-7 w-full"
                  />
                </div>
              </div>
            </div>
            <section className="overflow-x-auto">
              <table className="w-[1020px]" style={{ minWidth: "700px" }}>
                <thead className="bg-gray-50 font-bold p-4 text-left">
                  <tr>
                    <th className="p-2 ">S/N</th>
                    <th className="p-2">Name</th>
                    <th className="p-2">Email</th>
                    <th className="p-2">Phone number</th>
                    <th className="p-2">Contact</th>
                    <th className="p-2">Address</th>
                    <th className="p-2 text-nowrap">Verification stage</th>
                  </tr>
                </thead>
                <tbody className="px-4">
                  {searchResults.length > 0 && activeTab === "outright"
                    ? searchResults.map((data: any, index) => (
                        <tr
                          className="border-b border-gray-300 py-2"
                          key={index}
                        >
                          <td className="text-secondary p-2">
                            {index + outrightIndexOfFirstPost + 1}
                          </td>
                          <td className="p-2 text-nowrap">
                            {data.firstName ? data.lastName : null}{" "}
                            {data.lastName ? data.firstName : null}
                          </td>
                          <td className="p-2 text-nowrap">{data.email}</td>
                          <td className="p-2 text-nowrap">
                            {data?.phoneNumber || "No number"}
                          </td>
                          <td className="p-2 text-nowrap flex justify-evenly">
                            <button type="button" title="call customer">
                              <a href={`tel:${data?.phoneNumber}`}>
                                <FaPhone />
                              </a>
                            </button>
                            <button type="button" title="text via whatsapp">
                              <a
                                href={`https://wa.me/${data?.phoneNumber?.replace(
                                  /^0/,
                                  "234"
                                )}`}
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                <FaWhatsapp />
                              </a>
                            </button>
                          </td>
                          <td className="p-2 text-nowrap">
                            {data.deliveryAddress ? data.deliveryAddress : null}
                          </td>
                          <td className="p-2 text-nowrap">{data.isComplete}</td>
                          <td className="p-2">
                            {data.userPassword
                              ? data.userPassword
                              : "not found"}
                          </td>
                        </tr>
                      ))
                    : outrightCurrentItems.map((data: any, index: number) => {
                        return (
                          <tr
                            className="border-b border-gray-300 py-2"
                            key={index}
                          >
                            <td className="text-secondary p-2">
                              {index + outrightIndexOfFirstPost + 1}
                            </td>
                            <td className="p-2 text-nowrap">
                              {data.firstName ? data.lastName : null}{" "}
                              {data.lastName ? data.firstName : null}
                            </td>
                            <td className="p-2 text-nowrap">{data.email}</td>
                            <td className="p-2 text-nowrap">
                              {data?.phoneNumber || "No number"}
                            </td>
                            <td className="p-2 text-nowrap flex justify-evenly">
                              <button type="button" title="call customer">
                                <a href={`tel:${data?.phoneNumber}`}>
                                  <FaPhone />
                                </a>
                              </button>
                              <button type="button" title="text via whatsapp">
                                <a
                                  href={`https://wa.me/${data?.phoneNumber?.replace(
                                    /^0/,
                                    "234"
                                  )}`}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                >
                                  <FaWhatsapp />
                                </a>
                              </button>
                            </td>
                            <td className="p-2 text-nowrap">
                              {data.deliveryAddress
                                ? data.deliveryAddress
                                : null}
                            </td>
                            <td className="p-2 text-nowrap">
                              {data.isComplete}
                            </td>
                          </tr>
                        );
                      })}
                </tbody>
              </table>
            </section>
          </div>
          <section className="p-3 my-5">
            <Pagination
              length={
                searchResults.length > 0
                  ? searchResults.length
                  : outright.length
              }
              itemsPerPage={itemsPerPage}
              handlePagination={handlePaginationOutright}
              currentPage={outrightCurrentPage}
              prevPage={prevPageOutright}
              nextPage={nextPageOutright}
            />
          </section>
        </div>
      )}

      {viewUpdateCreditLimit && (
        <UpdateCreditLimit
          userData={userData}
          closeModal={() => setViewUpdateCreditLimit(false)}
        />
      )}

      {allBanksModal && (
        <AllBanksModal
          setAllBanksModal={setAllBanksModal}
          setAllBanksDetails={setAllBanksDetails}
          allBanksDetails={allBanksDetails}
          setSearchedUser={setSearchedUser}
          userId={userId}
        />
      )}

      {addBankModal && (
        <AddBankModal
          setAddBankModal={setAddBankModal}
          userToAddBank={userToAddBank}
        />
      )}

      {bvnModalOpen && (
        <BVNVerificationModal
          setBvnModalOpen={setBvnModalOpen}
          selectedBvn={selectedBvn}
        />
      )}

      {selfEmployedModalOpen && (
        <SelfEmployed
          setSelfEmployedModalOpen={setSelfEmployedModalOpen}
          selectedInfo={selectedInfo}
        />
      )}
      {employedModalOpen && (
        <Employed
          setEmployedModalOpen={setEmployedModalOpen}
          selectedInfo={selectedInfo}
        />
      )}
      {studentModalOpen && (
        <Student
          selectedInfo={selectedInfo}
          setStudentModalOpen={setStudentModalOpen}
        />
      )}
    </main>
  );
};

export default UserDetails;
