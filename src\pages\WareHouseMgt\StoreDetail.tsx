import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import { fetchStoreById, deleteProduct, fetchProductsByStore } from "./data";
import { Product, StoreDetailData } from "./types";
import AddProductModal from "../../components/modals/AddProductModal";
import { BsPencil, BsTrash2 } from "react-icons/bs";
import { FiFilter } from "react-icons/fi";
import AddCategoryModal from "../../components/modals/AddCategoryModal";

export default function StoreDetail() {
  const { storeId } = useParams<{ storeId: string }>();
  const [store, setStore] = useState<StoreDetailData | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddProductModal, setShowAddProductModal] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [activeTab, setActiveTab] = useState("product-stock");
  const productsPerPage = 8;

  useEffect(() => {
    const loadStoreAndProducts = async () => {
      try {
        if (storeId) {
          const storeData = await fetchStoreById(storeId);
          setStore(storeData);

          const productsData = await fetchProductsByStore(storeId);
          setProducts(productsData);
        }
      } catch (error) {
        console.error("Failed to fetch store details:", error);
      } finally {
        setLoading(false);
      }
    };

    loadStoreAndProducts();
  }, [storeId]);

  const handleAddProduct = () => {
    setEditingProduct(null);
    setShowAddProductModal(true);
  };

  const handleEditProduct = (product: Product) => {
    setEditingProduct(product);
    setShowAddProductModal(true);
  };

  const handleDeleteProduct = async (productId: string) => {
    if (window.confirm("Are you sure you want to delete this product?")) {
      try {
        if (storeId) {
          await deleteProduct(storeId, productId);
          setProducts(products.filter((p) => p.id !== productId));
        }
      } catch (error) {
        console.error("Failed to delete product:", error);
      }
    }
  };

  const handleProductAdded = (newProduct: Product) => {
    if (editingProduct) {
      setProducts(
        products.map((p) => (p.id === newProduct.id ? newProduct : p))
      );
    } else {
      setProducts([...products, newProduct]);
    }
    setShowAddProductModal(false);
  };

  const getAttributeClass = (attribute: Product["attribute"]) => {
    switch (attribute) {
      case "Top Selling":
        return "text-green-600";
      case "Selling":
        return "text-orange-500";
      case "Regular":
        return "text-red-500";
      default:
        return "text-gray-500";
    }
  };

  // Pagination
  const indexOfLastProduct = currentPage * productsPerPage;
  const indexOfFirstProduct = indexOfLastProduct - productsPerPage;
  const currentProducts = products.slice(
    indexOfFirstProduct,
    indexOfLastProduct
  );
  const totalPages = Math.ceil(products.length / productsPerPage);

  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-600"></div>
      </div>
    );
  }

  if (!store) {
    return (
      <div className="text-center py-8">
        <p className="text-lg text-gray-600">Store not found.</p>
        <Link
          to="/dashboard/warehouse-management"
          className="text-green-600 hover:underline mt-4 inline-block"
        >
          Back to Ware House List
        </Link>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center mb-6">
        <Link
          to="/dashboard/warehouse-management"
          className="flex items-center text-xl font-medium"
        >
          Ware House
        </Link>
        <span className="mx-2">→</span>
        <h1 className="text-xl font-medium">{store.name}</h1>
      </div>

      <div className="mb-6">
        <div className="flex border-b">
          <button
            className={`px-6 py-3 font-medium ${
              activeTab === "product-stock"
                ? "text-green-600 border-b-2 border-green-600"
                : "text-gray-500"
            }`}
            onClick={() => setActiveTab("product-stock")}
          >
            Product Stock
          </button>
          <button
            className={`px-6 py-3 font-medium ${
              activeTab === "categories"
                ? "text-green-600 border-b-2 border-green-600"
                : "text-gray-500"
            }`}
            onClick={() => setActiveTab("categories")}
          >
            Categories
          </button>
        </div>
      </div>

      {activeTab === "product-stock" && (
        <>
          <div className="flex justify-between mb-6">
            <div>
              {/* This would be a search input in a real app */}
              <input
                type="text"
                placeholder="Search"
                className="px-4 py-2 border rounded-md w-64"
              />
            </div>
            <div className="flex space-x-4">
              <button
                onClick={handleAddProduct}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Add Product
              </button>
              <button className="px-4 py-2 border rounded-md flex items-center">
                <FiFilter className="w-4 h-4 mr-2" />
                Filters
              </button>
            </div>
          </div>

          <div className="bg-white rounded-md shadow overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Image
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Product Name
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Category
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Price
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Piece
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Attributes
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      Action
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {currentProducts.map((product) => (
                    <tr key={product.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex-shrink-0 h-12 w-12">
                          <img
                            className="h-12 w-12 rounded-md object-cover"
                            src={
                              product.image ||
                              `/placeholder.svg?height=48&width=48`
                            }
                            alt={product.name}
                          />
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {product.name}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">
                          {product.category}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          N{product.price.toFixed(2)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">
                          {product.quantity}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div
                          className={`text-sm ${getAttributeClass(
                            product.attribute
                          )}`}
                        >
                          {product.attribute}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleEditProduct(product)}
                            className="text-gray-600 hover:text-gray-900 p-1 rounded-md hover:bg-gray-100"
                          >
                            <BsPencil className="w-5 h-5" />
                          </button>
                          <button
                            onClick={() => handleDeleteProduct(product.id)}
                            className="text-red-600 hover:text-red-900 p-1 rounded-md hover:bg-gray-100"
                          >
                            <BsTrash2 className="w-5 h-5" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Pagination */}
          <div className="flex justify-between items-center mt-6">
            <div className="text-sm text-gray-500">
              Showing data 1 to {Math.min(indexOfLastProduct, products.length)}{" "}
              of {products.length} entries
            </div>
            <div className="flex space-x-1">
              <button
                onClick={() => paginate(currentPage - 1)}
                disabled={currentPage === 1}
                className="px-3 py-1 rounded-md border disabled:opacity-50"
              >
                &lt;
              </button>
              {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => (
                <button
                  key={i + 1}
                  onClick={() => paginate(i + 1)}
                  className={`px-3 py-1 rounded-md ${
                    currentPage === i + 1 ? "bg-green-600 text-white" : "border"
                  }`}
                >
                  {i + 1}
                </button>
              ))}
              {totalPages > 5 && <span className="px-3 py-1">...</span>}
              {totalPages > 5 && (
                <button
                  onClick={() => paginate(totalPages)}
                  className={`px-3 py-1 rounded-md border ${
                    currentPage === totalPages ? "bg-green-600 text-white" : ""
                  }`}
                >
                  {totalPages}
                </button>
              )}
              <button
                onClick={() => paginate(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="px-3 py-1 rounded-md border disabled:opacity-50"
              >
                &gt;
              </button>
            </div>
          </div>
        </>
      )}

      {activeTab === "categories" && <CategoriesTab storeId={storeId || ""} />}

      {activeTab === "service-fee" && (
        <div className="bg-white rounded-md shadow p-6">
          <h2 className="text-lg font-medium mb-4">Service and Delivery Fee</h2>
          <p className="text-gray-500">
            This feature is currently under development. Please check back
            later.
          </p>
        </div>
      )}

      {showAddProductModal && (
        <AddProductModal
          storeId={storeId || ""}
          product={editingProduct}
          onClose={() => setShowAddProductModal(false)}
          onProductAdded={handleProductAdded}
        />
      )}
    </div>
  );
}

// Categories Tab Component
const CategoriesTab = ({ storeId }: { storeId: string }) => {
  const [categories, setCategories] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddCategoryModal, setShowAddCategoryModal] = useState(false);

  useEffect(() => {
    // In a real app, you would fetch categories for this specific store
    // For demo purposes, we'll use dummy data
    const dummyCategories = [
      { id: "1", name: "FROZEN FOODS", image: "/products/shrimp.jpg" },
      { id: "2", name: "FROZEN FOODS", image: "/products/groceries.jpg" },
      { id: "3", name: "FROZEN FOODS", image: "/products/salad.jpg" },
      { id: "4", name: "FROZEN FOODS", image: "/products/pasta.jpg" },
      { id: "5", name: "FROZEN FOODS", image: "/products/peppers.jpg" },
      { id: "6", name: "FROZEN FOODS", image: "/products/shrimp2.jpg" },
    ];

    setTimeout(() => {
      setCategories(dummyCategories);
      setLoading(false);
    }, 500);
  }, []);

  const handleAddCategory = () => {
    setShowAddCategoryModal(true);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between mb-6">
        <button
          onClick={handleAddCategory}
          className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
        >
          Add Category
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {categories.map((category) => (
          <div
            key={category.id}
            className="bg-white rounded-md shadow overflow-hidden"
          >
            <div className="h-48 overflow-hidden">
              <img
                src={category.image || `/placeholder.svg?height=192&width=384`}
                alt={category.name}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="p-4 text-center">
              <h3 className="font-medium text-gray-900">{category.name}</h3>
            </div>
          </div>
        ))}
      </div>

      {showAddCategoryModal && (
        <AddCategoryModal
          onClose={() => setShowAddCategoryModal(false)}
          onCategoryAdded={(category) => {
            setCategories([...categories, category]);
            setShowAddCategoryModal(false);
          }}
        />
      )}
    </div>
  );
};
