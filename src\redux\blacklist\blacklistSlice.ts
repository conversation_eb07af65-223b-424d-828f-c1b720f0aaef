import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { getAllBlacklisted, addToBlacklist, removeFromBlacklist } from "../thunk";

interface BlacklistUser {
  _id: string;
  customerId: {
    virtualAccount: {
      walletbalance: number;
    };
    categoryType: string;
    _id: string;
    email: string;
    phoneNumber: string;
    firstName: string;
    lastName: string;
    id: string;
  };
  walletId: {
    _id: string;
    loanLimit: number;
    creditLimit: number;
    lastLoanRecord: {
      orderAmount: number;
      totalLoan: number;
      paidLoan: number;
      amountLeft: number;
      montlyPayBack: number;
      payLeft: number;
      loanInitialDate: string;
      payDate: string;
      loanDate: string;
      _id: string;
    };
  };
  createdAt: string;
  updatedAt: string;
}

interface BlacklistState {
  blacklist: BlacklistUser[];
  status: "idle" | "loading" | "succeeded" | "failed";
  error: string | null;
}

const initialState: BlacklistState = {
  blacklist: [],
  status: "idle",
  error: null,
};

const blacklistSlice = createSlice({
  name: "blacklist",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // Get all blacklisted users
      .addCase(getAllBlacklisted.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(getAllBlacklisted.fulfilled, (state, action: PayloadAction<{ blacklist: BlacklistUser[] }>) => {
        state.status = "succeeded";
        state.blacklist = action.payload.blacklist;
        state.error = null;
      })
      .addCase(getAllBlacklisted.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload as string || "Failed to fetch blacklist";
      })
      
      // Add to blacklist
      .addCase(addToBlacklist.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(addToBlacklist.fulfilled, (state) => {
        state.status = "succeeded";
        state.error = null;
      })
      .addCase(addToBlacklist.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload as string || "Failed to add user to blacklist";
      })
      
      // Remove from blacklist
      .addCase(removeFromBlacklist.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(removeFromBlacklist.fulfilled, (state) => {
        state.status = "succeeded";
        state.error = null;
      })
      .addCase(removeFromBlacklist.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload as string || "Failed to remove user from blacklist";
      });
  },
});

export default blacklistSlice.reducer;
