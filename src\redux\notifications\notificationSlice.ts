import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export interface Notification {
  id: string;
  type: 'user' | 'order' | 'payment';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  createdAt: string;
}

interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  isConnected: boolean;
}

// Get stored notifications from localStorage if available
const storedNotifications = localStorage.getItem('notifications');
const storedUnreadCount = localStorage.getItem('unreadCount');

const initialState: NotificationState = {
  notifications: storedNotifications ? JSON.parse(storedNotifications) : [],
  unreadCount: storedUnreadCount ? parseInt(storedUnreadCount) : 0,
  isConnected: false,
};

const notificationSlice = createSlice({
  name: "notifications",
  initialState,
  reducers: {
    setConnectionStatus: (state, action: PayloadAction<boolean>) => {
      state.isConnected = action.payload;
    },
    addNotification: (state, action: PayloadAction<Notification>) => {
      state.notifications.unshift(action.payload);
      if (!action.payload.read) {
        state.unreadCount += 1;
      }
      // Save to localStorage
      localStorage.setItem('notifications', JSON.stringify(state.notifications));
      localStorage.setItem('unreadCount', state.unreadCount.toString());
    },
    markAsRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.id === action.payload);
      if (notification && !notification.read) {
        notification.read = true;
        state.unreadCount = Math.max(0, state.unreadCount - 1);
        // Save to localStorage
        localStorage.setItem('notifications', JSON.stringify(state.notifications));
        localStorage.setItem('unreadCount', state.unreadCount.toString());
      }
    },
    markAllAsRead: (state) => {
      state.notifications.forEach(notification => {
        notification.read = true;
      });
      state.unreadCount = 0;
      // Save to localStorage
      localStorage.setItem('notifications', JSON.stringify(state.notifications));
      localStorage.setItem('unreadCount', '0');
    },
    clearNotifications: (state) => {
      state.notifications = [];
      state.unreadCount = 0;
      // Save to localStorage
      localStorage.setItem('notifications', JSON.stringify([]));
      localStorage.setItem('unreadCount', '0');
    },
    setNotifications: (state, action: PayloadAction<Notification[]>) => {
      state.notifications = action.payload;
      state.unreadCount = action.payload.filter(n => !n.read).length;
      // Save to localStorage
      localStorage.setItem('notifications', JSON.stringify(action.payload));
      localStorage.setItem('unreadCount', state.unreadCount.toString());
    },
  },
});

export const {
  setConnectionStatus,
  addNotification,
  markAsRead,
  markAllAsRead,
  clearNotifications,
  setNotifications,
} = notificationSlice.actions;

export default notificationSlice.reducer;

