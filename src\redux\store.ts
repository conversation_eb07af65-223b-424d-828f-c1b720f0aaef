import { configureStore } from "@reduxjs/toolkit";
import authReducer from "./auth/authSlice";
import productReducer from "./products/productSlice";
import orderReducer from "./orders/orderSlice";
import verificationReducer from "./verification/verificationSlice";
import logisticsReducer from "./logistics/logisticsSlice";
import vendorReducer from "./vendors/vendorSlice";
import feeReducer from "./fees/feeSlice";
import userReducer from "./users/userSlice";
import warehouseReducer from "./warehouse/warehouseSlice";
import stateGovReducer from "./stateGov/stateGovSlice";
import churchReducer from "./church/churchSlice";
import privateOrgReducer from "./privateOrg/privateOrgSlice";
import notificationReducer from "./notifications/notificationSlice";
import transactionReducer from "./transactions/transactionSlice";

const store = configureStore({
  reducer: {
    auth: authReducer,
    products: productReducer,
    orders: orderReducer,
    verification: verificationReducer,
    logistics: logisticsReducer,
    vendors: vendorReducer,
    fees: feeReducer,
    users: userReducer,
    warehouse: warehouseReducer,
    stateGovs: stateGovReducer,
    churches: churchReducer,
    privateOrgs: privateOrgReducer,
    notifications: notificationReducer,
    transactions: transactionReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export default store;
