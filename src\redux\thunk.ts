import { createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import toast from "react-hot-toast";
import {
  AddWarehousePayload,
  AddWarehouseResponse,
  CreateStatePayload,
  CreateStateResponse,
  State,
  UpdateStorePayload,
  UpdateStoreResponse,
} from "../types/types";

const auth: any = sessionStorage.getItem("authState");
const jsonData = JSON.parse(auth);
const token = jsonData?.token;

export const login = createAsyncThunk(
  "login",
  async (credentials: { email: string; password: string }, thunkAPI) => {
    try {
      const response = await axios.post(
        `${process.env.REACT_APP_API_URL}/admin-login`,
        credentials
      );
      sessionStorage.setItem("adminLevel", response.data.user.adminLevel);

      return response.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response.data.error);
    }
  }
);

export const getAllProducts = createAsyncThunk(
  "getAllProducts",
  async (_, thunkAPI) => {
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/get-products`
      );
      return response.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response.data.error);
    }
  }
);

export const getProduct = createAsyncThunk(
  "getProduct",
  async (productId: any, thunkAPI) => {
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/get-product/${productId}`
      );
      return response.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response.data.error);
    }
  }
);

export const getAllProductType = createAsyncThunk(
  "getAllProductType",
  async (_, thunkAPI) => {
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/getAllProductType`
      );

      return response.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response.data.error);
    }
  }
);

export const updateSingleProduct = createAsyncThunk(
  "updateProduct",
  async ({ data, id }: { data: FormData; id: string }, thunkAPI) => {
    try {
      const response = await axios.put(
        `${process.env.REACT_APP_API_URL}/update-product/${id}`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      return response.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const deleteProductType = createAsyncThunk(
  "deleteProductType",
  async (categoryId: string, thunkAPI) => {
    try {
      const response = await axios.delete(
        `${process.env.REACT_APP_API_URL}/deleteProductType/${categoryId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const deleteProduct = createAsyncThunk(
  "deleteProduct",
  async (productId: string, thunkAPI) => {
    try {
      const response = await axios.delete(
        `${process.env.REACT_APP_API_URL}/delete-product/${productId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      return response.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const createProductItem = createAsyncThunk(
  "createProductItem",
  async (data: any, thunkAPI) => {
    try {
      const response = await axios.post(
        `${process.env.REACT_APP_API_URL}/create-product`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      return response.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response.data.error);
    }
  }
);
export const createProductType = createAsyncThunk(
  "createProductType",
  async (data: any, thunkAPI) => {
    try {
      const response = await axios.post(
        `${process.env.REACT_APP_API_URL}/createProductType`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      return response.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const verificationStageOne = createAsyncThunk(
  "verificationStageOne",
  async (_, thunkAPI) => {
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/verification-stage-one`
      );

      return response.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const getOrders = createAsyncThunk("getOrders", async (_, thunkAPI) => {
  try {
    const response = await axios.get(
      `${process.env.REACT_APP_API_URL}/getAllOrdersByCustomer`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    return response.data;
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error.response.data);
  }
});

export const getSingleCustomer = createAsyncThunk(
  "getSingleCustomer",
  async (id: string, thunkAPI) => {
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/getSingleCustomer/${id}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      return response.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const getAllRepaymentHistory = createAsyncThunk(
  "getAllRepaymentHistory",
  async (_, thunkAPI) => {
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/getAllRepaymentHistory`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      return response.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const allCustomerNextPay = createAsyncThunk(
  "allCustomerNextPay",
  async (_, thunkAPI) => {
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/allCustomerNextPay`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      return response.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const getDefaulters = createAsyncThunk(
  "getDefaulters",
  async (_, thunkAPI) => {
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/defaulters`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      return response.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const approveOrder = createAsyncThunk(
  "approveOrder",
  async (data: any, thunkAPI) => {
    try {
      const response = await axios.post(
        `${process.env.REACT_APP_API_URL}/approve-order`,
        data,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      toast.success("Order has been approved!");

      return response.data;
    } catch (error: any) {
      if (error.response.status === 502 || error.response.status === 503) {
        toast.error("Network error");
      }
      toast.error("Something went wrong");
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const getAllBanner = createAsyncThunk(
  "getAllBanner",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/getAllBanner`,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      return res.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const deleteBanner = createAsyncThunk(
  "deleteBanner",
  async (id: string, thunkAPI) => {
    try {
      const response = await axios.delete(
        `${process.env.REACT_APP_API_URL}/deleteBanner/${id}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      return response.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const getAllDispatch = createAsyncThunk(
  "getAllDispatch",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/getAllDispatcherDetails`,
        {
          headers: {
            Authorization: token,
          },
        }
      );

      return res.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const logisticDashboard = createAsyncThunk(
  "logisticDashboard",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/logisticDashboardStatistic`,
        {
          headers: {
            Authorization: token,
          },
        }
      );

      return res.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const logisticAssignDetails = createAsyncThunk(
  "logisticAssignDetails",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/getDetailsForLogisticAssignDelivery`,
        {
          headers: {
            Authorization: token,
          },
        }
      );

      return res.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const createDispatcher = createAsyncThunk(
  "createDispatcher",
  async (
    dispatcherData: {
      firstName: string;
      lastName: string;
      email: string;
      password: string;
      phoneNumber: string;
      image: string;
    },
    thunkAPI
  ) => {
    try {
      // Ensure the image field is included
      const payload = {
        ...dispatcherData,
        image:
          dispatcherData.image ||
          "https://jy0s2swu0k.ufs.sh/f/LKSiq2B5GRS8nqXTASCYrqb8EcQRiuAhmfJoHsDWN5U347eg",
      };

      const res = await axios.post(
        `${process.env.REACT_APP_API_URL}/createDispatcher`,
        payload,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      return res.data;
    } catch (error: any) {
      console.error("Error creating dispatcher:", error);
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to create dispatcher"
      );
    }
  }
);

export const getAllVendors = createAsyncThunk(
  "getAllVendors",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${process.env.REACT_APP_API_URL}/vendors`, {
        headers: {
          Authorization: token,
        },
      });
      return res.data;
    } catch (error: any) {
      console.error(error);
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const getAllStates = createAsyncThunk<
  State[],
  void,
  { rejectValue: string }
>("warehouse/getAllStates", async (_, thunkAPI) => {
  const auth: any = sessionStorage.getItem("authState");
  const jsonData = JSON.parse(auth || "{}");
  const token = jsonData?.token;

  if (!token) {
    return thunkAPI.rejectWithValue("Authentication token not found.");
  }

  try {
    const res = await axios.get(
      `${process.env.REACT_APP_API_URL}/getAllStates`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return res.data as State[];
  } catch (error: any) {
    console.error("Error fetching states:", error);
    const errorMsg =
      error.response?.data?.error || error.message || "Failed to fetch states";
    return thunkAPI.rejectWithValue(errorMsg);
  }
});

export const getStateById = createAsyncThunk<
  State,
  string,
  { rejectValue: string }
>("warehouse/getStateById", async (stateId, thunkAPI) => {
  const auth: any = sessionStorage.getItem("authState");
  const jsonData = JSON.parse(auth || "{}");
  const token = jsonData?.token;

  if (!token) {
    return thunkAPI.rejectWithValue("Authentication token not found.");
  }

  try {
    const res = await axios.get(
      `${process.env.REACT_APP_API_URL}/getStateById/${stateId}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return res.data as State;
  } catch (error: any) {
    console.error(`Error fetching state ${stateId}:`, error);
    const errorMsg =
      error.response?.data?.error ||
      error.message ||
      "Failed to fetch state details";

    return thunkAPI.rejectWithValue(errorMsg);
  }
});

export const updateWarehouseStore = createAsyncThunk<
  UpdateStoreResponse,
  UpdateStorePayload,
  { rejectValue: string }
>("warehouse/updateStore", async ({ stateId, storeId, data }, thunkAPI) => {
  const auth: any = sessionStorage.getItem("authState");
  const jsonData = JSON.parse(auth || "{}");
  const token = jsonData?.token;

  if (!token) {
    return thunkAPI.rejectWithValue("Authentication token not found.");
  }
  if (!stateId) {
    return thunkAPI.rejectWithValue("State ID is missing for update.");
  }
  if (!storeId) {
    return thunkAPI.rejectWithValue("Store ID is missing for update.");
  }

  try {
    const response = await axios.put(
      `${process.env.REACT_APP_API_URL}/stateManagement/${stateId}/warehouse/${storeId}`,
      data,
      { headers: { Authorization: `Bearer ${token}` } }
    );

    if (response.data && response.data.updatedState) {
      console.log(
        "Thunk: Update successful, received updatedState:",
        response.data.updatedState
      );
      return response.data.updatedState as UpdateStoreResponse;
    } else if (response.data && response.data._id && response.data.wareHouse) {
      console.log(
        "Thunk: Update successful, received State directly:",
        response.data
      );
      return response.data as UpdateStoreResponse;
    } else {
      console.warn(
        "Thunk: Update response structure unexpected:",
        response.data
      );
      return thunkAPI.rejectWithValue(
        "Invalid response structure from update API."
      );
    }
  } catch (error: any) {
    console.error(
      `Error updating store (StateID: ${stateId}, StoreID: ${storeId}):`,
      error
    );
    const errorMsg =
      error.response?.data?.error ||
      error.response?.data?.message ||
      error.message ||
      "Failed to update store";
    return thunkAPI.rejectWithValue(errorMsg);
  }
});

export const createStateAndWarehouse = createAsyncThunk<
  CreateStateResponse,
  CreateStatePayload,
  { rejectValue: string }
>("warehouse/createState", async (payload, thunkAPI) => {
  const auth: any = sessionStorage.getItem("authState");
  const jsonData = JSON.parse(auth || "{}");
  const token = jsonData?.token;

  if (!token) {
    return thunkAPI.rejectWithValue("Authentication token not found.");
  }
  if (!payload) {
    return thunkAPI.rejectWithValue("Data Cannot be empty.");
  }

  try {
    const response = await axios.post(
      `${process.env.REACT_APP_API_URL}/createStateManagement`,
      payload,
      { headers: { Authorization: `Bearer ${token}` } }
    );

    return response.data as CreateStateResponse;
  } catch (error: any) {
    console.error("Error creating state:", error);
    const errorMsg =
      error.response?.data?.error ||
      error.response?.data?.message ||
      error.message ||
      "Failed to create state";
    return thunkAPI.rejectWithValue(errorMsg);
  }
});

export const addWarehouseToState = createAsyncThunk<
  AddWarehouseResponse,
  AddWarehousePayload,
  { rejectValue: string }
>("warehouse/addStoreToState", async ({ stateId, data }, thunkAPI) => {
  const auth: any = sessionStorage.getItem("authState");
  const jsonData = JSON.parse(auth || "{}");
  const token = jsonData?.token;

  if (!token) {
    return thunkAPI.rejectWithValue("Authentication token not found.");
  }
  if (!stateId) {
    return thunkAPI.rejectWithValue("State ID is missing.");
  }

  try {
    const response = await axios.post(
      `${process.env.REACT_APP_API_URL}/stateManagement/${stateId}/warehouse`,
      data,
      { headers: { Authorization: `Bearer ${token}` } }
    );
    if (response.data && response.data.updatedState) {
      return response.data.updatedState as AddWarehouseResponse;
    } else {
      return thunkAPI.rejectWithValue(
        "Invalid response structure from add warehouse API."
      );
    }
  } catch (error: any) {
    console.error("Error adding warehouse to state:", error);
    const errorMsg =
      error.response?.data?.error ||
      error.response?.data?.message ||
      error.message ||
      "Failed to add warehouse";
    return thunkAPI.rejectWithValue(errorMsg);
  }
});

export const getFees = createAsyncThunk("getFees", async (_, thunkAPI) => {
  try {
    const res = await axios.get(
      `${process.env.REACT_APP_API_URL}/get-allFees`,
      {
        headers: {
          Authorization: token,
        },
      }
    );
    return res.data;
  } catch (error: any) {
    console.error(error);
    return thunkAPI.rejectWithValue(error.response.data);
  }
});

export const getFlexibleUsers = createAsyncThunk(
  "getFlexibleUsers",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/getapprovedCustomers`,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      return res.data;
    } catch (error: any) {
      console.error(error);
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const getEventsGroupedByUser = createAsyncThunk(
  "getEventsGroupedByUser",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/getEventsGroupedByUser`,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      return res.data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response?.data?.message || error.message || "Failed to fetch transactions");
    }
  }
);

export const getOutrightUsers = createAsyncThunk(
  "getOutrightUsers",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/getOutrightCustomers`,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      return res.data;
    } catch (error: any) {
      console.error(error);
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const getAllIncompleteUsers = createAsyncThunk(
  "getAllIncompleteUsers",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/incompleteRegisteration`,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      return res.data;
    } catch (error: any) {
      console.error(error);
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const checkTokenExpiration = () => {
  const tokenTimestamp = sessionStorage.getItem("tokenTimestamp");
  if (auth && token && tokenTimestamp) {
    const currentTime = new Date().getTime();
    const expirationTime =
      new Date(parseInt(tokenTimestamp, 10)).getTime() + 24 * 60 * 60 * 1000;

    if (currentTime >= expirationTime) {
      sessionStorage.clear();
      sessionStorage.removeItem("tokenTimestamp");
      return false;
    }
    return true;
  }
  return false;
};

export const formatNumber = (number: number) => {
  return new Intl.NumberFormat().format(number);
};

export const getDashboardStats = async () => {
  try {
    const response = await axios.get(
      `${process.env.REACT_APP_API_URL}/getDashboardStatistic`,
      {
        headers: {
          Authorization: token,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    console.error("Failed to fetch:", error);
  }
};

export const verificationStageTwo = createAsyncThunk(
  "verificationStageTwo",
  async (_, thunkAPI) => {
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_API_URL}/verification-stage-two`
      );

      return response.data.customers.filter(
        (user: any) => user.accountType === "flexible"
      );
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const fetchCategories = async () => {
  try {
    const res = await axios.get(
      `${process.env.REACT_APP_API_URL}/onboardingCategories`,
      {
        headers: {
          Authorization: token,
        },
      }
    );
    const data = res.data.categories.filter(
      (item: any) => item.category.toLowerCase() === "oyo state government"
    );
    return data[0]?.categoryType;
  } catch (error) {
    console.error("An error occurred: ", error);
  }
};

export const fetchStateGovernmentDetails = createAsyncThunk(
  "fetchStateGovernmentDetails",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/onboardingCategories`,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      const data = res.data.categories.filter(
        (item: any) => item.category.toLowerCase() === "oyo state government"
      );
      return data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const fetchChurchDetails = createAsyncThunk(
  "fetchChurchDetails",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/onboardingCategories`,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      const data = res.data.categories.filter(
        (item: any) => item.category.toLowerCase() === "church"
      );
      return data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const fetchPrivateOrgDetails = createAsyncThunk(
  "fetchPrivateOrgDetails",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(
        `${process.env.REACT_APP_API_URL}/onboardingCategories`,
        {
          headers: {
            Authorization: token,
          },
        }
      );
      const data = res.data.categories.filter(
        (item: any) => item.category.toLowerCase() === "private organization"
      );
      return data;
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);
