import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { getEventsGroupedByUser } from "../thunk";

interface TransactionDetails {
  _id: string;
  transactionReference: string;
  amount: number;
  eventType: string;
  message: string;
  createdAt: string;
}

interface UserDetails {
  _id: string;
  email: string;
  virtualAccount: {
    walletbalance: number;
  };
  firstName: string;
  lastName: string;
  accountType?: string;
  categoryType?: string;
}

interface TransactionGroup {
  userDetails: UserDetails;
  transactionDetails: TransactionDetails[];
}

interface TransactionsState {
  transactionGroups: TransactionGroup[];
  status: "idle" | "loading" | "succeeded" | "failed";
  error: string | null;
}

const initialState: TransactionsState = {
  transactionGroups: [],
  status: "idle",
  error: null,
};

const transactionsSlice = createSlice({
  name: "transactions",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getEventsGroupedByUser.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(getEventsGroupedByUser.fulfilled, (state, action: PayloadAction<{ data: TransactionGroup[] }>) => {
        state.status = "succeeded";
        state.transactionGroups = action.payload.data;
        state.error = null;
      })
      .addCase(getEventsGroupedByUser.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload as string || "Failed to fetch transactions";
      });
  },
});

export default transactionsSlice.reducer;
