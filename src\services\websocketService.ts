import { io, Socket } from 'socket.io-client';
import store from '../redux/store';
import { addNotification, setConnectionStatus, Notification } from '../redux/notifications/notificationSlice';
import toast from 'react-hot-toast';

class WebSocketService {
  private socket: Socket | null = null;
  private lastBadgeCounts = { registrations: 0, orders: 0, payments: 0 };

  connect(_userId: string) {
    if (this.socket?.connected) {
      console.log('WebSocket already connected');
      return;
    }
    // Use the exact WebSocket URL from your screenshot
    const socketUrl = process.env.REACT_APP_API_URL?.replace('/v1/api', '');

    this.socket = io(socketUrl, {
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true
    });

    this.setupEventListeners();
  }

  private setupEventListeners() {
    if (!this.socket) return;

    this.socket.on('connect', () => {

      // Join the admin room to receive notifications
      this.socket?.emit('joinAdminRoom');

      // Add confirmation listener for room joining
      this.socket?.on('joinedAdminRoom', (data) => {
        console.log('Successfully joined admin room:', data);
      });

      // Add error listener for room joining
      this.socket?.on('joinAdminRoomError', (error) => {
        console.error('Failed to join admin room:', error);
      });

      store.dispatch(setConnectionStatus(true));
      toast.success('Connected to real-time notifications');
    });

    this.socket.on('disconnect', () => {
      store.dispatch(setConnectionStatus(false));
      toast.error('Disconnected from real-time notifications');
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      store.dispatch(setConnectionStatus(false));
      toast.error('Failed to connect to real-time notifications');
    });

    const handleUserRegistration = (data: any) => {
      const notification: Notification = {
        id: `user_${Date.now()}`,
        type: 'user',
        title: 'New User Registration',
        message: `${data.firstName || data.fullName || 'Unknown'} ${data.lastName || ''} has registered`,
        data: data,
        read: false,
        createdAt: new Date().toISOString(),
      };

      store.dispatch(addNotification(notification));
      toast.success(`New user registered: ${data.firstName || data.fullName || 'Unknown'} ${data.lastName || ''}`);
    };

    // Listen for admin notifications from backend
    this.socket.on('adminNotification', (data: any) => {
      console.log('Received adminNotification:', data);

      // Handle the notification format from your backend
      if (data && data.title) {
        let notificationType = 'user';

        // Determine notification type based on title
        if (data.title.toLowerCase().includes('user') || data.title.toLowerCase().includes('registration')) {
          notificationType = 'user';
        } else if (data.title.toLowerCase().includes('order')) {
          notificationType = 'order';
        } else if (data.title.toLowerCase().includes('payment')) {
          notificationType = 'payment';
        }

        // Create notification without exposing sensitive data
        const notification: Notification = {
          id: `${notificationType}_${Date.now()}`,
          type: notificationType as 'user' | 'order' | 'payment',
          title: data.title,
          message: data.title, // Use title as message
          data: { type: notificationType, timestamp: new Date().toISOString() },
          read: false,
          createdAt: new Date().toISOString(),
        };

        store.dispatch(addNotification(notification));
        toast.success(`New notification: ${data.title}`);
        console.log('Notification added to store:', notification);
      } else if (data.badgeCounts) {
        // Fallback: Handle old badgeCounts format if it exists
        console.log('Received badgeCounts:', data.badgeCounts);
        const { registrations, orders, payments } = data.badgeCounts;

        // Check if this is the first time we're receiving counts (initialization)
        const isFirstTime = this.lastBadgeCounts.registrations === 0 &&
                           this.lastBadgeCounts.orders === 0 &&
                           this.lastBadgeCounts.payments === 0;

        if (isFirstTime) {
          this.lastBadgeCounts = { registrations, orders, payments };
          return;
        }

        // Calculate differences and show notifications for increases
        const regDiff = registrations - this.lastBadgeCounts.registrations;
        const orderDiff = orders - this.lastBadgeCounts.orders;
        const paymentDiff = payments - this.lastBadgeCounts.payments;

        if (regDiff > 0) {
          const notification: Notification = {
            id: `user_${Date.now()}`,
            type: 'user',
            title: 'New User',
            message: regDiff === 1 ? `Just Signed-Up and approved orders` : `${regDiff} users Just Signed-Up and approved orders`,
            data: { type: 'user', timestamp: new Date().toISOString() },
            read: false,
            createdAt: new Date().toISOString(),
          };
          store.dispatch(addNotification(notification));
          toast.success(regDiff === 1 ? 'New user registered!' : `${regDiff} new users registered!`);
        }

        if (orderDiff > 0) {
          const notification: Notification = {
            id: `order_${Date.now()}`,
            type: 'order',
            title: 'New Order',
            message: orderDiff === 1 ? `New order has been placed` : `${orderDiff} new orders have been placed`,
            data: { type: 'order', timestamp: new Date().toISOString() },
            read: false,
            createdAt: new Date().toISOString(),
          };
          store.dispatch(addNotification(notification));
          toast.success(orderDiff === 1 ? 'New order received!' : `${orderDiff} new orders received!`);
        }

        if (paymentDiff > 0) {
          const notification: Notification = {
            id: `payment_${Date.now()}`,
            type: 'payment',
            title: 'New Payment',
            message: paymentDiff === 1 ? `New payment has been received` : `${paymentDiff} new payments have been received`,
            data: { type: 'payment', timestamp: new Date().toISOString() },
            read: false,
            createdAt: new Date().toISOString(),
          };
          store.dispatch(addNotification(notification));
          toast.success(paymentDiff === 1 ? 'New payment received!' : `${paymentDiff} new payments received!`);
        }

        this.lastBadgeCounts = { registrations, orders, payments };
      } else {
        console.log('Received adminNotification with unexpected format:', data);
      }
    });

    // Fallback event handlers for individual events
    this.socket.on('registrations', (data) => {
      console.log('Received registrations event:', data);
      handleUserRegistration(data);
    });
    this.socket.on('newUserRegistered', (data) => {
      console.log('Received newUserRegistered event:', data);
      handleUserRegistration(data);
    });
    this.socket.on('userRegistered', (data) => {
      console.log('Received userRegistered event:', data);
      handleUserRegistration(data);
    });

    // Add a catch-all listener to see what events are being received
    this.socket.onAny((eventName, ...args) => {
      console.log('WebSocket received event:', eventName, args);
    });
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      store.dispatch(setConnectionStatus(false));
    }
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  emit(event: string, data: any) {
    if (this.socket?.connected) {
      this.socket.emit(event, data);
    }
  }

  joinAdminRoom() {
    if (this.socket?.connected) {
      console.log('Manually joining admin room...');
      this.socket.emit('joinAdminRoom');
    } else {
      console.log('Cannot join admin room - socket not connected');
    }
  }
}

const websocketService = new WebSocketService();

export default websocketService;


