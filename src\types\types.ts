export type SideBarProps = {
  tabNavigation: boolean;
  toggleTabNavigation: () => void;
  openModal: () => void;
  role?: any;
  showSidebar: boolean;
  setShowSidebar: any;
  setSrc: any;
};

export type NavbarProps = {
  tabNavigation: boolean;
  toggleTabNavigation: () => void;
  showSidebar: boolean;
  setShowSidebar: any;
};

export type Navbar2Props = {
  tab: string;
  openModal?: () => void;
};

export type ButtonProps = {
  className?: string;
  children: React.ReactNode | string;
  disabled?: boolean;
  onClick?: any;
  type?: any;
};

export type ModalProps = {
  children: React.ReactNode;
  open: boolean;
  onClose?: () => void;
  className?: string;
};

export type LogoutModalProps = {
  closeModal: () => void;
};

export type ProductsModalProps = {
  closeModal: () => void;
  productsObj: any;
};

export type TodayPaymentProps = {
  openModal: () => void;
};
export type PaymentProps = {
  openModal: () => void;
  setCustomerId: (value: any) => any;
};

export type AddNewCustomerProps = {
  openModal: () => void;
  closeModal: () => void;
  setCustomerAcctNo: React.Dispatch<React.SetStateAction<string>>;
};

export type SalesHistoryProps = {
  openModal: () => void;
  openSuccessModal: () => void;
  openProductModal: () => void;
  setProductsObj: React.Dispatch<React.SetStateAction<any>>;
  setCustomerId: React.Dispatch<React.SetStateAction<any>>;
  openCalendarModal: () => void;
};

export type CalenderProps = {
  selectedDate: Date;
  setSelectedDate: (date: Date) => void;
  sectionClasses?: string;
  setShowingDatePicker?: React.Dispatch<React.SetStateAction<boolean>>;
  closeModal: () => void;
  customerId?: any;
};

export type ItemCardProps = {
  productId: string;
  img: string;
  name: string;
  description: string;
  outright: string;
  flexible: string;
};

export type ItemModalProps = {
  status: string;
  productId: string;
  img: string;
  name: string;
  description: string;
  outright: string;
  flexible: string;
  setShowModal: (value: boolean) => void;
  setShowCheckoutModal: (value: boolean) => void;
};

export type replyProps = {
  message: string;
  time: string;
};

export type ViewItemsProps = {
  setShowModal: (value: boolean) => void;
};

export type SuccessModalProps = {
  heading: string;
  details: string;
  setShowSuccess: (value: boolean) => void;
};

export type CartModalProps = {
  customerAcctNo: string;
  setShowModal: (value: boolean) => void;
  setShowSuccess: (value: boolean) => void;
};

export type AddProductModalProps = {
  setShowSuccess: (value: boolean) => void;
  openModal: () => void;
  closeModal: () => void;
};

export type products = {
  imageUrl: string;
  _id: string;
  image: string;
  images: string[] | undefined;
  name: string;
  location: string;
  description: string;
  measurement: string;
  price: number;
};

export type MarketersModalProps = {
  name: string;
  email: string;
  phoneNo: string;
  address: string;
  staffID: string;
  image: string;
  setShowModal: (value: boolean) => void;
};

export type MigrationModalProps = {
  selectedMarketer: string;
  setShowMigrationModal: (value: boolean) => void;
  setShowSuccess: (value: boolean) => void;
};

export type SuccessProps = {
  closeModal?: () => void;
  customerAcctNo?: string;
};

export type CustomerTableProps = {
  openCalender: () => void;
  openCustomerModal: () => void;
  setCustomerId: React.Dispatch<React.SetStateAction<any>>;
};

export type AdminCustomerTableProps = {
  openCalender: () => void;
  openCustomerModal: () => void;
  setCustomerId: React.Dispatch<React.SetStateAction<any>>;
};

export interface WareHouseInState {
  storeName: string;
  location: string;
  contactInfo: string;
  _id: string;
}
export interface WarehouseStore {
  storeName: string;
  location: string;
  contactInfo: string;
  _id: string;
}

export interface State {
  _id: string;
  state: string;
  stateIcon: string;
  wareHouse?: WarehouseStore[];
  createdAt?: string;
  updatedAt?: string;
  __v?: number;
}

export interface Store {
  _id: string;
  name: string;
  address: string;
  location: string;
  code: string;
  phone: string;
}

interface UpdateStoreData {
  storeName: string;
  location: string;
  contactInfo: string;
}

export interface UpdateStorePayload {
  stateId: string;
  storeId: string;
  data: UpdateStoreData;
}

export interface CreateStatePayload {
  state: string;
  stateIcon: string;
  wareHouse: Omit<WarehouseStore, "_id">[];
}
export interface AddWarehousePayload {
  stateId: string;
  data: Omit<WarehouseStore, "_id">;
}
export type UpdateStoreResponse = State;
export type CreateStateResponse = State;
export type AddWarehouseResponse = State;
